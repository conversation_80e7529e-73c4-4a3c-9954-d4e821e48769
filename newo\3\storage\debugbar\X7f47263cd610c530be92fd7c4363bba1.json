{"__meta": {"id": "X7f47263cd610c530be92fd7c4363bba1", "datetime": "2025-06-14 02:51:08", "utime": **********.287632, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.155528, "end": **********.287666, "duration": 1.****************, "duration_str": "1.13s", "measures": [{"label": "Booting", "start": **********.155528, "relative_start": 0, "end": **********.076087, "relative_end": **********.076087, "duration": 0.****************, "duration_str": "921ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.076104, "relative_start": 0.****************, "end": **********.287669, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "212ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020450000000000003, "accumulated_duration_str": "20.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1526618, "duration": 0.01727, "duration_str": "17.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.45}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.19699, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.45, "width_percent": 6.553}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2547128, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 91.002, "width_percent": 8.998}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749869464702%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZwK003Y3FtdmRQWnpUTGJmWFN2S2c9PSIsInZhbHVlIjoibkdYdnhhUEtScStSL2ExZEprY3diU1JBN2ZSSmhaTThodWZSWSt2Q1ZvRUNzdXJ2S1dsY3dZQ2JPbUpKNWJhQXRiTVpsTCtURnhiQWFJdVBJdEdBcUlIeGUzUjFoa2FYOXdwVDFUdXRYbU1ubUlJRmhxN1NMRlY3dzdEQlBlbHJ5aS8xSWFOUkFZZXc3cHdEalp5N0xMWWt3NjkyMlNrL0ZQNkRjdWwvVnl0cEFDSm5UTWdmVkJ4VmpzMnIzbmdyTGR1Y2Z0dUNsV25rbjkxMWNPQ2EvTmgwanRFTkVEU1BtSFFKKzNYdkVJb3p3bUVMMy9RaGFpK05WYnhxc2w0VkNoKytMdmpPbTVFVVAxUzlVQWRqSzhxc2kwemxJb0JXV2RrMjZxNnpEUFV2MVA4WXVrZUFjNEJOWGJRUmU1QkZ6NmtGYVBHNzBkKzlnRzQ3UkJLZG9KdFhpQU8wNjFzZGJyNjhPUFQrWWd6SXVQVnNZUXBReFY1OUF2amQ5SFRreXozczFDV0w1VzY2RC9kTGc4YUEwZzlqTWh5S2ltb0RBYTI5Sm9RSWVid2I3QkFpbFhDemgzTGZXVmFDTGxSODBOK2xKb0pOMENWRUlGTG5CbjFLVSs2ZXNFU01qVWVnaEVCOUF4OWM0bXNpRFpTbFAwMkZlY1hnbXh5VTkwbnoiLCJtYWMiOiIzMzdkYTQ0ZGNlZDNlNTk0Mzc4YjhkMDExMWI1MmRhNTI4YmY4MmMyMDlmZjNmZTk5MzZjN2JkZDAyYmY3MjczIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdLZ3ZHSU00UUxrS241dEdaK1ZxbXc9PSIsInZhbHVlIjoiMlNwVVFnTitwMC9zc1ZXakJlU1NlSnB6Q2tYbWNhWEY4YTFyTjFHTGF1dVFhYTYzZFFQQzRiM24rN0EyZURsRUFYMU5qMjRuRWtDMzFwT2E5Y3d6NXFlMVVRSHZJNFFNVVlJQlM1N1pyTUZybHlnTXYzY29DMm5hVEh6a0Y4N2lzSWhqSXlNVVdMWUFyRm1IbzJvcmRtbU5NY2N4dW1FYlhwRm5BdGtvNzFVRG1VMEhQT2xFRWpXd2tyWUl6NG0weW9ldVpDME5xYWw1eXl1aWU1WUlyMkNWa29yQmxqd1lXbXpTNTNHQ2VwempGR1ovRUhqY3FxdTEvdVQrTlhMaG5xRnJvUnFJZUM1VFpGUE5UQ1ZxMDhqdi9YV2Y4RTI5SHpMcTFPVGJSd1g2RXVIbkhwaFhoK3lCbUROMzFEdzZhSUdBWHBENVJNRzhZaHNjVFo2b2pYUjh6dXZaaXh4bE96clg1UDZLZzhYeFhrdnlYS2t3QjFDWkhadjFCVXhRN0d0eWFwYW0rdkd3VVdheDhnSTA2OUZXWW1lTktVTjNwNnNBU2pLYU9VMG1CTk04ek8xNjIxbzI2c1Q1ekQ1ZDZ2TUlZWGxMWHpjRnlFMGpCWEtaeGRpOXRrYTRQeS90L3JPTUExMitmemxIU2piUTBJd2JqMDJ0Z2RGc2YwRVUiLCJtYWMiOiI5MDI3MWUyOWNkZDRjZmNjOTI1NTViY2IzNWI2ZjlmNWZlOWRlZDY1YmY1YThkYjJhYWVhZGJjM2M3ZGNmOTM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-132951436 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t6clpBhZQEC0tcIVAqApy3fwEvNxCUuY7Y8VEjFB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132951436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1315112750 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 02:51:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhjcTNpZEdqcUc4ck4ydVYwMDBvU1E9PSIsInZhbHVlIjoiaGxEYUpzRzNkeEpQaWxaV1ZRRTZBYUIzcE12Z1VCQ2hPTE1udTh4SXB6V2N4V1MweWp4cTc4b2VRYW52Wjk0YzdxM1p2M1dNOEJnU25pTnNvWnJFMmRUQWlrVzJDczVSa3I4VlI1MmNJSWgrSGhEdmNwYVJOQXk1ekRzUk51SE12RnpncW9ZN2xGa0p1cCsxYkRpUWxHOHg5RUxiazYxbG1KeUhmMDJOM2FTei9WS1YrSU51a0RkL1NsMTRxWTRqa2VSYm1pUldvQlJ5cXg2cy9IZWdUcHVhN1Y3dGNxeUNoQzFJaE5Oa21FanhvOVREUDdINitaY0JxclZmQWpKeVljajJEV21ES2dybVJIRVdzVG5DeWgyLzF2SG5lNkU1aW1jY0pDUFVsQlA1bUhTck1UQmFSMldQV0FweGF4N250NnB2SnpxRjNLbW1WdGEveTJ5N0RCVERnRjloYWpGMFN4b0hWR0x1UHY5aXQ1YXVkL1YyYWYxN3BsdWRyY3pxQ3ZLYWhZZ2NVK1VReFlaNWRIejg4cjBTSDFtcHBmRGNiTGpkN2Q3L3pWd3N0c3BGMDh5UEdVZ1NFbU5UNUcrcjdGNC9WaXlqb2NndytyOFQ2NGk1ZnI2QXc3c2pLVTM2Qk92NlIzd2o3bVNXUVFmSUora05BcUJvWmlzVkxXaDkiLCJtYWMiOiI4Y2FkYTQ4MjA1ZGJiZWU4MDk5YjYzMWFhNmRiOGFlNzA3MTg4ZjllMjIxMmUyYmY3Y2Q5NzYxMGUyMGY5NDE2IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9lREF1ejZZUjhBaW11aGd4dTNFQXc9PSIsInZhbHVlIjoiZkwrYUU5ZHJRVmtiL3hhZmRzU0srL3NNTis5Y2lJM0dtSVlBSGZYMVhpMWdNcStwNThwZXQ3RWMrb3Zha0tHWTZSS01XMXZRd2xyTmU4Q1J2VWlwT0M3akdlVHdGY25UZ0hoWG5DZjQza2llWDdLMkdWYkFzSFhYMEJjU2dEaUxlOGRVU0h1elVKaHNSakdwc3RZQWovS2hwWlpPUk1RUmliYnU2ZndUMHB5YTJCcUQ4OWhROFB6T2R6eko1RTU3NjYzOXQ4RnN1Z0tKS3dOd3NqRUttKzcrWFJvRkwxQ3VtdE1JSVp2QjlmMGxwbVVLUWFTcGZJWVY1MFhQQnc3eTdoNWszMS9jWHdxak4vaUpYRlZGalZDU3dIY0JvbVlvMFloeXZMWXhYNmsvNDdCRUI3MDI4U2F5eTJCM1Y3UVVwcWZwOWl1YVpqWnoyM1N5MmwzL1dFWitRRUdGbVVLY3VoQ0VFM3V2WFptckhGUGs0ejNWNkRoUDFEWlRlTWdzOGJ5UmtKbHFud1p0Mm5ad2Z6dlJON3BZY0t4aUF6OTdJZmQ1VHJJMVJ1N1ZYTHFQN2dBMkNOUUtTTUFkQ1pWN2loYmRsazlpMlZoMzlGa0FjQVEzK0xrb0Q3bmUxZmswd1VReUlpeWpHc3FCSlZReUMydXpERnNES3oxU1hBQ1QiLCJtYWMiOiI4YjYyYzc3NDZkZmE4NDdlMWJlOTMwYWM3ODZhODE2M2UwY2MyYzA4ZjFmYWYwOTU1NzcwZTFiOTRkNTA0YjI1IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhjcTNpZEdqcUc4ck4ydVYwMDBvU1E9PSIsInZhbHVlIjoiaGxEYUpzRzNkeEpQaWxaV1ZRRTZBYUIzcE12Z1VCQ2hPTE1udTh4SXB6V2N4V1MweWp4cTc4b2VRYW52Wjk0YzdxM1p2M1dNOEJnU25pTnNvWnJFMmRUQWlrVzJDczVSa3I4VlI1MmNJSWgrSGhEdmNwYVJOQXk1ekRzUk51SE12RnpncW9ZN2xGa0p1cCsxYkRpUWxHOHg5RUxiazYxbG1KeUhmMDJOM2FTei9WS1YrSU51a0RkL1NsMTRxWTRqa2VSYm1pUldvQlJ5cXg2cy9IZWdUcHVhN1Y3dGNxeUNoQzFJaE5Oa21FanhvOVREUDdINitaY0JxclZmQWpKeVljajJEV21ES2dybVJIRVdzVG5DeWgyLzF2SG5lNkU1aW1jY0pDUFVsQlA1bUhTck1UQmFSMldQV0FweGF4N250NnB2SnpxRjNLbW1WdGEveTJ5N0RCVERnRjloYWpGMFN4b0hWR0x1UHY5aXQ1YXVkL1YyYWYxN3BsdWRyY3pxQ3ZLYWhZZ2NVK1VReFlaNWRIejg4cjBTSDFtcHBmRGNiTGpkN2Q3L3pWd3N0c3BGMDh5UEdVZ1NFbU5UNUcrcjdGNC9WaXlqb2NndytyOFQ2NGk1ZnI2QXc3c2pLVTM2Qk92NlIzd2o3bVNXUVFmSUora05BcUJvWmlzVkxXaDkiLCJtYWMiOiI4Y2FkYTQ4MjA1ZGJiZWU4MDk5YjYzMWFhNmRiOGFlNzA3MTg4ZjllMjIxMmUyYmY3Y2Q5NzYxMGUyMGY5NDE2IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9lREF1ejZZUjhBaW11aGd4dTNFQXc9PSIsInZhbHVlIjoiZkwrYUU5ZHJRVmtiL3hhZmRzU0srL3NNTis5Y2lJM0dtSVlBSGZYMVhpMWdNcStwNThwZXQ3RWMrb3Zha0tHWTZSS01XMXZRd2xyTmU4Q1J2VWlwT0M3akdlVHdGY25UZ0hoWG5DZjQza2llWDdLMkdWYkFzSFhYMEJjU2dEaUxlOGRVU0h1elVKaHNSakdwc3RZQWovS2hwWlpPUk1RUmliYnU2ZndUMHB5YTJCcUQ4OWhROFB6T2R6eko1RTU3NjYzOXQ4RnN1Z0tKS3dOd3NqRUttKzcrWFJvRkwxQ3VtdE1JSVp2QjlmMGxwbVVLUWFTcGZJWVY1MFhQQnc3eTdoNWszMS9jWHdxak4vaUpYRlZGalZDU3dIY0JvbVlvMFloeXZMWXhYNmsvNDdCRUI3MDI4U2F5eTJCM1Y3UVVwcWZwOWl1YVpqWnoyM1N5MmwzL1dFWitRRUdGbVVLY3VoQ0VFM3V2WFptckhGUGs0ejNWNkRoUDFEWlRlTWdzOGJ5UmtKbHFud1p0Mm5ad2Z6dlJON3BZY0t4aUF6OTdJZmQ1VHJJMVJ1N1ZYTHFQN2dBMkNOUUtTTUFkQ1pWN2loYmRsazlpMlZoMzlGa0FjQVEzK0xrb0Q3bmUxZmswd1VReUlpeWpHc3FCSlZReUMydXpERnNES3oxU1hBQ1QiLCJtYWMiOiI4YjYyYzc3NDZkZmE4NDdlMWJlOTMwYWM3ODZhODE2M2UwY2MyYzA4ZjFmYWYwOTU1NzcwZTFiOTRkNTA0YjI1IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315112750\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-271258935 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271258935\", {\"maxDepth\":0})</script>\n"}}