#!/bin/bash

# =============================================================================
# سكريبت نشر أوامر الاستلام لدور Cashier
# =============================================================================

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# إعدادات الخادم (يجب تعديلها حسب بيئتك)
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"

# دالة لطباعة الحالة
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# دالة لطباعة النجاح
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# دالة لطباعة الخطأ
print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# دالة لطباعة التحذير
print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# دالة للتحقق من نجاح العملية
check_success() {
    if [ $? -eq 0 ]; then
        print_success "$1 تم بنجاح"
    else
        print_error "فشل في $1"
        exit 1
    fi
}

# بداية السكريبت
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}  نشر أوامر الاستلام لدور Cashier  ${NC}"
echo -e "${BLUE}================================${NC}"
echo ""

# التحقق من وجود الملفات المطلوبة
print_status "التحقق من وجود الملفات المطلوبة..."

required_files=(
    "resources/views/partials/admin/menu.blade.php"
    "app/Http/Controllers/ReceiptOrderController.php"
    "resources/views/receipt_order/index.blade.php"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "الملف غير موجود: $file"
        exit 1
    fi
done

print_success "جميع الملفات المطلوبة موجودة"

# المرحلة 1: رفع الملفات
print_status "🚀 المرحلة 1: رفع الملفات المحدثة..."

# رفع ملف القائمة الجانبية
print_status "رفع ملف القائمة الجانبية..."
scp resources/views/partials/admin/menu.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/partials/admin/
check_success "رفع ملف القائمة الجانبية"

# رفع الكونترولر
print_status "رفع الكونترولر..."
scp app/Http/Controllers/ReceiptOrderController.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Http/Controllers/
check_success "رفع الكونترولر"

# رفع ملف العرض
print_status "رفع ملف العرض..."
scp resources/views/receipt_order/index.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/receipt_order/
check_success "رفع ملف العرض"

# المرحلة 2: ضبط الصلاحيات
print_status "🔐 المرحلة 2: ضبط صلاحيات الملفات..."
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/resources/views/partials/admin/menu.blade.php"
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php"
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/resources/views/receipt_order/index.blade.php"
check_success "ضبط صلاحيات الملفات"

# المرحلة 3: مسح الكاش
print_status "🧹 المرحلة 3: مسح الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan cache:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan view:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan config:clear"
check_success "مسح الكاش"

# المرحلة 4: إعادة تحميل الكاش (اختياري)
print_status "⚡ المرحلة 4: إعادة تحميل الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:cache"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan config:cache"
check_success "إعادة تحميل الكاش"

# انتهاء النشر
echo ""
echo -e "${GREEN}================================${NC}"
echo -e "${GREEN}       تم النشر بنجاح! 🎉        ${NC}"
echo -e "${GREEN}================================${NC}"
echo ""

print_success "تم نشر جميع التغييرات بنجاح"
print_warning "يرجى اختبار النظام للتأكد من عمل جميع الوظائف"

echo ""
echo -e "${BLUE}📋 خطوات الاختبار:${NC}"
echo "1. تسجيل الدخول بحساب لديه دور Cashier"
echo "2. التحقق من ظهور 'أوامر الاستلام' في قائمة POS"
echo "3. اختبار عرض قائمة أوامر الاستلام"
echo "4. اختبار إنشاء أمر استلام جديد"
echo "5. اختبار عرض تفاصيل أمر الاستلام"
echo "6. اختبار الطباعة وتحميل PDF"

echo ""
print_status "انتهى النشر بنجاح! ✨"
