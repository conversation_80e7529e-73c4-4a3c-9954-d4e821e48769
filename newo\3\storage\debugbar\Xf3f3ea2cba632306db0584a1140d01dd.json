{"__meta": {"id": "Xf3f3ea2cba632306db0584a1140d01dd", "datetime": "2025-06-14 02:51:26", "utime": **********.538564, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.519797, "end": **********.538595, "duration": 1.****************, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": **********.519797, "relative_start": 0, "end": **********.377436, "relative_end": **********.377436, "duration": 0.****************, "duration_str": "858ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.377453, "relative_start": 0.****************, "end": **********.538599, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01744, "accumulated_duration_str": "17.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.445972, "duration": 0.01483, "duration_str": "14.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.034}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.478061, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.034, "width_percent": 6.135}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.516886, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 91.17, "width_percent": 8.83}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749869483217%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhsclYrNTFaNXhtWXVzSDh0WEI1c2c9PSIsInZhbHVlIjoiNzFER3BVdGxBd3ZIM2t3eUdNR082SjhRNWN1SEZhZndDRDdCVmZOM2ZIemZQTHBsZlp3SjYxVThxcVJkSWhPZEF3cVhEOEVGVTJlR0x2RHhCRXFpU2dwcU02cVlGdSsrWGY4SE5mc004MHBnY2FkSE1iekRpQ3dZbjZzZHhXcGNaWXNTMkxXdnY1cWw2ZVFnczdtdnBXV0J2QmkrK1BVcVB0clNUWnFGSm9WeVJsaU56U0ZHL1FiRSsyT2VrZXZjSGhTZ1I0bVdLL3d4UjJvS0d0dStBMGFzZ2FlQmx5SkY3VXN0eEZvV3pudVp1QzZaRGhGcVgxYVIyV2x4eDBvaktQNHFXcHZiY21uN3A5S1gzMnNrRGkvQk9Jc2dsbS9GVS9GM1NoZFhrNldTQkhvUSttRDNRaU1tS2xKbWNCSXUvMURFQXBBMTZiQ1hTVDdHdDFMQlIyKzJKSmNMSkxNUHNJS2pHZTNMaUhVbWlBUFMyNXRXeC92ZTJ6ajFaZ2tkQ25JcUhOL3dva1BzN21ibmlKV2pTaWRzd3lrNmRoZCt2bktHd1cvNmJkazFjaXl0b2JkZmZmOWp4RzkxbFUyVzRPeGtNODg3UDVRenFTRFBBSkQ2aERDOXlLeGxzVnNZTTA4TEpRTndmSFFscVpSOXJCN0N6WTV6NzJLZnk3Y2giLCJtYWMiOiIyNzUwNDY4YThlNGY2OGI4NDk3ZmI3YmZkZGVlNzU4NzIxOTYyNThkNWRjNDYzZWJlZTVmNmRlZTRkNzc0OTY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9NQzdYckNrR3l1VmxuSkwvT1JldUE9PSIsInZhbHVlIjoibjBvbHEvT1VQT21oSE5YSFd1OHh5NGRSK0YzeG5YV0ZUN2laYXFCcUtIMXNiakJXN0txVEZnKzdVd0tMbkhKRW1zSVorTWNjc0d5em1HbVRMM3I5bkFET2t2Vy9aR2tHamNkQzdKMXFyODRlNElHMlhMamRnUmc5c0h2bkFGVTZ6WUM5dWhXVmdvMzI5K3g4cWVncGtQZnBIRmV1bnhCWjZ4SERDVzd6SUREZmFIRnpUQ2VzZEx5RThRNGp1Wk45aDRqTTR3RFA2U29vOExpbU5lbjFYNEd6NkxSemw2WG9yYzY0bklNMEx2ejhjR2VGcGxsbFBkbW5CV2ViNmIva2lyZHY2ZVhaWURZSU1YNEJFekN4M3o2Q0laQ3pSY0FvSXc5TDdtWHJwcUYvNTlzb29ZR2FPUW4rTE04UG5jWFZ0Y0trVlpOQUh2Ri9vWWwyNTE1Rm1mSS9tMmk1UFNuMG43VG5JUVpZd0lpU0MzYzlQaXFQSHhPNTNQYzc1ZkVkenk0Ry9DTTBJeGRKMWpzZlo3RFh6aTRSZk4vQkxJNU1ISzRBa2pnYURKSVJSVTRLbDM2VFdLUXRsc09QeVp2Vmp6RmZiazZGVlZzKzdNU1NibTFPaEVKc1VncVgrTjJFMzNpcnF4dFJTN2wzbUdOZVJDcVhMWkE3TGxETFNkS1QiLCJtYWMiOiIxYmQ5YjZiMjgwNDE3NjBiYjZhMWZiZDhkOTYxYzA1ZDgyN2ViZjRjNzk5NmQyM2ZjM2QyMjI3NzcxZTZiOGE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1262291189 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t6clpBhZQEC0tcIVAqApy3fwEvNxCUuY7Y8VEjFB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262291189\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-31779828 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 02:51:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtmVmUwUjViNUcrR2lTUkxQWFB1S2c9PSIsInZhbHVlIjoiVGIvR3NGQ3VUMzRRVTRDYm4xb3loczNXUGIva0hVOE95dHB4RFFPSHhac0Mvci80VEVHY2IzV1NvZHpHaGRXT0ttczY3NitZUFJjV2dmdWMvN1NGZUlxSnZMYkx0MkhzU3lQdEt5d0JvL3RnNmtPMlhjMElNMDNwOUVXbytqRmU4Z1FoblUvWWxmUEhaT2R4SDZCamlpby94a1hHUlo4djNWcHRQUUJlcTFhZVBYcWlxYWNVR2F0aSt2bXlOZVdxakh2S1ZMNmdtbnBCcDhPZnoyaGVpWkR6cVVGbENZOE44N1o4bmY0YVFOWktrQ2ZBZDBrQkxCdXAxL3A2eXE4OWxLUkNBYkpFbXkvWCtmY0MxTytBZWlKL0ZSeWtvMkR2TnRNYVdkVDgzOGE5L05NRzdJcGNiUjY4Sk5vNVpzOWV5bDgyTVNNT2NiMDk3ekxVU3lmaUhtbzJDRnlzby8yRnhaSnZ3UTFLQkUrR3lGaHFrL1FQNG92NS9tZ1VCUGlhZEJmYS9kV1JTdGNxRzh5MVZuQmZSYzRqNWVnSFBPZGd6OVlUNlFVNzM4bW1uTWJFWGZyVC9MTUpVOGZ6OUsrK2FPV096Y2d3L0RsckNLV0dFYXA4bVhic2c5N0lyQVF0ZWZpUkFlWENRcEl1bEpUM1pLejVyTlF1RGVnUUtmcHoiLCJtYWMiOiIwOGZkOWQyNzM0MDhmM2M3YTU4ZmEyZWZhYzQ0ZmE2YTQ5MTZkMjE5MzJlMzI4YjQ1NDllNTM5MmRiMmNmNDY0IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im50WXBFU3dRZWk5NEtRM051UGVPd0E9PSIsInZhbHVlIjoiRElaeFNueWZibjd0THJmQWN4VjZVSm1TdlBSUG1YOUpjcEh5S1RlWGpMU2lZN0xRRm5meUJOYjVva1hrMCs0REJhWE9RamVxUUM4Ymk5SzJML3RoaUVUZkhSUXE2MnVsbUJiSm5vOHVVMURkQTlzQUF2T3BmNkJBVG1NaTZJVTM0SjYyZzZIbVhHOE1iaW5JUWpubE5WRjlVaHgrZzQ2ZDVySXFoTHQ3TkloYkxBQTRVUmw0SzRMdW1mRmUxNHJ0aHM1bDNiMHh3M1kxUDZpZlZYUjBNa00zaGhCVFg5TEVrMStoWmtCd0RCemo4cm5keU14K2NQNlZ1MlUwL1NIM3VDbzRZRks2K2UrNXNGUDY1N2NFdlJFenZNQ0trQVAzRzcvQXk1Mk0wYmZWZ0ZiMVBpRG1zWm55bkY0RERsbG1oZHZhYU1XYTlRSGx2KzQyeTZaOG91SHB1eHU0Qzg2aTN6Q0diTUxJOThSTi9rNnlrakFnMjBhNGN1a3JJZGNqd1o0Rjd6bUd0NENmOVFWbHM0ZjRlRE1JdThLak1scTB1bmNOL0hsUkN5K3ByMFIrOXozSlpYTG1UZmFPajhBSmVsTDFOdDRXYmxSMUdNU1pvSVdtZ1dHNDNsbkFBYzZzMktacU02U1Y4RlhtQTUyS056MjRLbE5VbnRDZFozY1QiLCJtYWMiOiJmMTdhZWQzZmVkZWUzNzRmZTI0ZWY2YWJiOTMzY2JiMzU2MTUyM2NlMDU5ZjI4OWViZGJhNWU1YzE3MzU4NThmIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtmVmUwUjViNUcrR2lTUkxQWFB1S2c9PSIsInZhbHVlIjoiVGIvR3NGQ3VUMzRRVTRDYm4xb3loczNXUGIva0hVOE95dHB4RFFPSHhac0Mvci80VEVHY2IzV1NvZHpHaGRXT0ttczY3NitZUFJjV2dmdWMvN1NGZUlxSnZMYkx0MkhzU3lQdEt5d0JvL3RnNmtPMlhjMElNMDNwOUVXbytqRmU4Z1FoblUvWWxmUEhaT2R4SDZCamlpby94a1hHUlo4djNWcHRQUUJlcTFhZVBYcWlxYWNVR2F0aSt2bXlOZVdxakh2S1ZMNmdtbnBCcDhPZnoyaGVpWkR6cVVGbENZOE44N1o4bmY0YVFOWktrQ2ZBZDBrQkxCdXAxL3A2eXE4OWxLUkNBYkpFbXkvWCtmY0MxTytBZWlKL0ZSeWtvMkR2TnRNYVdkVDgzOGE5L05NRzdJcGNiUjY4Sk5vNVpzOWV5bDgyTVNNT2NiMDk3ekxVU3lmaUhtbzJDRnlzby8yRnhaSnZ3UTFLQkUrR3lGaHFrL1FQNG92NS9tZ1VCUGlhZEJmYS9kV1JTdGNxRzh5MVZuQmZSYzRqNWVnSFBPZGd6OVlUNlFVNzM4bW1uTWJFWGZyVC9MTUpVOGZ6OUsrK2FPV096Y2d3L0RsckNLV0dFYXA4bVhic2c5N0lyQVF0ZWZpUkFlWENRcEl1bEpUM1pLejVyTlF1RGVnUUtmcHoiLCJtYWMiOiIwOGZkOWQyNzM0MDhmM2M3YTU4ZmEyZWZhYzQ0ZmE2YTQ5MTZkMjE5MzJlMzI4YjQ1NDllNTM5MmRiMmNmNDY0IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im50WXBFU3dRZWk5NEtRM051UGVPd0E9PSIsInZhbHVlIjoiRElaeFNueWZibjd0THJmQWN4VjZVSm1TdlBSUG1YOUpjcEh5S1RlWGpMU2lZN0xRRm5meUJOYjVva1hrMCs0REJhWE9RamVxUUM4Ymk5SzJML3RoaUVUZkhSUXE2MnVsbUJiSm5vOHVVMURkQTlzQUF2T3BmNkJBVG1NaTZJVTM0SjYyZzZIbVhHOE1iaW5JUWpubE5WRjlVaHgrZzQ2ZDVySXFoTHQ3TkloYkxBQTRVUmw0SzRMdW1mRmUxNHJ0aHM1bDNiMHh3M1kxUDZpZlZYUjBNa00zaGhCVFg5TEVrMStoWmtCd0RCemo4cm5keU14K2NQNlZ1MlUwL1NIM3VDbzRZRks2K2UrNXNGUDY1N2NFdlJFenZNQ0trQVAzRzcvQXk1Mk0wYmZWZ0ZiMVBpRG1zWm55bkY0RERsbG1oZHZhYU1XYTlRSGx2KzQyeTZaOG91SHB1eHU0Qzg2aTN6Q0diTUxJOThSTi9rNnlrakFnMjBhNGN1a3JJZGNqd1o0Rjd6bUd0NENmOVFWbHM0ZjRlRE1JdThLak1scTB1bmNOL0hsUkN5K3ByMFIrOXozSlpYTG1UZmFPajhBSmVsTDFOdDRXYmxSMUdNU1pvSVdtZ1dHNDNsbkFBYzZzMktacU02U1Y4RlhtQTUyS056MjRLbE5VbnRDZFozY1QiLCJtYWMiOiJmMTdhZWQzZmVkZWUzNzRmZTI0ZWY2YWJiOTMzY2JiMzU2MTUyM2NlMDU5ZjI4OWViZGJhNWU1YzE3MzU4NThmIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31779828\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-242597717 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242597717\", {\"maxDepth\":0})</script>\n"}}