{"__meta": {"id": "X12d644f632dd81d7ea2424b1515b93f0", "datetime": "2025-06-14 03:08:39", "utime": **********.535661, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749870518.195967, "end": **********.535691, "duration": 1.339724063873291, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749870518.195967, "relative_start": 0, "end": **********.363268, "relative_end": **********.363268, "duration": 1.1673009395599365, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.363288, "relative_start": 1.167320966720581, "end": **********.535695, "relative_end": 4.0531158447265625e-06, "duration": 0.1724071502685547, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45157800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01942, "accumulated_duration_str": "19.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.451421, "duration": 0.01678, "duration_str": "16.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.406}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4931471, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.406, "width_percent": 5.973}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.512547, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.379, "width_percent": 7.621}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-349986602 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749870508641%7C4%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ino0TXNleGhkYkxZRXc4S0o3VWZTaWc9PSIsInZhbHVlIjoiVGdSWHpjSHJYbVlTOTdGSkxlbzloSVZBNUJjTU44MkxoTk01cGd5RTdsWTNpN0tjd0t5OXdmYUJlNUZzeUJzRmo0YkJ3K2hkaDdmVEI0Z0lMMThmOUNrRGJkNjd2WlVpUHhXeVQyOStUdDNoSDVBNVJ3c3hLZUx4UzV5UHB3OHNVSXc1cHdqQjVnYmZmajBXNWtXOXYveG5ZTTJ4Z0tCM3k2ZCtDemZNdXJyVEFuR1BUbXU2UFc4d2NDZktIc3dEMVBnUHRacFdUdjMyZnQrZDk0U1hTdFlUbG1xVG92U1FULzdUcVhxVlFKNTRlVmhBeDdNSnBGSUJ3L1JjUUFZUG5zQmZRNGw4T1p2NHJ6OFJackNCZ25zdGRXeFpxVThrMjliTWt0SFA5cGlhZXNFTy9SSUJRUGI0V3AzOG4rN0MxQ0xQdnJYaENFREpOZE1ySmRMZ0dmWHVVMTl0NjRxRlZTSk5KWThWbFpUdjJZYnJ4S3lQZ1VNNEFtZk5IV21BQUlua2VmRUg1K29IUkNlL1NZdEplUDVVVm8vTlRvdjdEalpDQXhUMktPamVyZDZ1R0Y4Y1pab0k1bmhhR0djZkNnSmJlMTBSYnBqKzhBajJ1NmVWckJVYWxuWHNDNVVValM1NDdRN0dqenRaV05YM2RsWE1iQnczQjB3ZXphRmsiLCJtYWMiOiI5MTEwZDZhNWZlZDBhZWEzMmNmMjI4OTQ1ZGUzNGU3N2IyMzdmODZkMWRhMjQyYWQ5ODEzNGJjMzljYWQ3NmNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNDdzZxT3Bya0ZHTUZoQWFpZEJONVE9PSIsInZhbHVlIjoiT3VJejl3OCtrY1BhdkR3VXVhanpoYlFlQW15R1NtYTR2OUl5c0RWVHFTSDJaNk1CSm5zRk5NK2QxbGkrR0c3RTY3RkltcmdLWTBKU1Jsc3FyNm11dU4xc2VvTDJXTmgwaXI2SmM4SGxPblh3VjRSaVpTUUNMRlQwdnRxSElBbUg1VkRuM3Z3d2V4Y2N4ekNIU2RaTlVVZmYvRnhkZlQxQU84WHZrczdBbzhjSDByN0tvb1RuZWFVSXBxTWRSUThmUjRjRnZGTkE5QnVGRjVRUkI0WVpaRTQ2ZERVYWJNanhsZ2tRTnU1dm5pRVM2Y2NZS01iOUcweitEYVZ1R3IwNVkzK2pTbTZxUGlGSHMvRHVnN2J6QXk1ZHYvK0U0bm1BYituZWwvV1oxVkxPTGx6OXd1Z3RqVVNGMVloKzk5KzZnWVU5RWdSZ29HT0pYVGtGS3VJNlZyOElaK2ZpUW9US0lPaDkwUkFTQ2phNWlTQmdDTzdSeGdtYXRVcVRiVEZkWTBGMzRlZ2c0eU9Bc2VsSHUzaTdvdEQyNmdNTnVmUjU0Q2M2SElRSmFSWHcrMkJ5eWxxSWxqZGVsSjBWZytJN2c4M216ME9yRzl3c3g2azdsa2Nwbk55Tm1SbXZLZE93b3VvTFZmL0FtZVE5Q3h4dnlSMmtDSzJYS0pCeDdBRDUiLCJtYWMiOiIwMjU4ZmJlMjA0NzM2MjgwODg4ODFhM2YyNDU3ODAxYjI3ZmU4Y2ZmN2U1ZDI0Zjg1YzFlZGVkNmQwYThkOTBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349986602\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ryrmc4WtBXBhputH8HGgfbcOnkPzoklQrtYTNOUV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1294788873 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 03:08:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZ0MWN0ZzZ5ZWRDWDF3dnRVSUthbGc9PSIsInZhbHVlIjoiYUExRnFWcGZzclFNU3hBWk9kUHhtd1NQTDVNNk9GZjY3VGozMjM0c0liR1VqUjlQWjJCa29vVEFJZmpZY1NlbUE1eVVrVUQraW1Cd09VdXFFWnBSMWlrdEtDU2JqWHNaYUMyNWwxSHVGUFVpZ2U5NytmQkpUWEoyMU9lSzRBTGxXc052blVNUVFFODBqeHRCcnNKT2JXaGVHSEM0S3VDc3FPVnVXUXJldlRkUk9WSzB6WE9nc2dnalFHZjBoYlp1U1lRakJtd2hVNzFrMThHckxsZWlyNzBSVm5FL2NZOWRzRjVYTHdqV2pKMzl6TTlzUUg4bG9VWFBsREk2M0NPM2tNTTRFK09KamIwS2NQd3o0emhuNExtdENTZG5SRVRDc0tQeTZ2WDFxYU1PYmlBRDE3clMwZDNKWVlGTUwwVHNnQTd3VkZSRjRsZkpNOVFYeUt2NWZGMUdtb2NyNEZBMU5UcG5uK2gyTnpYbkNCRmVveGNiUVBzOG9XNG5FcVNoWlJsRkptaTFFdWxtNTE5L3FNdDJ3cUNOS1hEUXEyd1BMWVJHY0xtd0RDUmdReXkzZ3ZPOEtDcjNzaHp2VU9jeE42VnYzaWgyZm9rUkQ3ZEFrY3NzZDhhNmE3Kzl4K3dhMDBMeWg3aGNEREJnNktJcmZvK3hpSGVHaVNLVzcxbmwiLCJtYWMiOiJiMjFhZTE5NDE4MjIxMDI4Y2JjYjc4MWE0MzA2YWRlMjIzMjcyZWFiZmM5MTc4ZTRmZjEwNDNkNjcxMGUwZjkyIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 05:08:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlEzbHFnSmxJT2kxNW5sVEVKN01Cb0E9PSIsInZhbHVlIjoibzdTZDBZWHdWTTR0RXRGaytWVWZ0WlhrcVQ5R0FVR2R0djRuQWo2UWtRamoyQXRpNFpMbG9aaDZTOUd2SC83MUxmNWhJdTMxUVlwVG5uaCswQ0tEL1MxL29nOGZlcy9HRlZLNzBXYUMwZ2VQeU04RzV5ZGNVUEV4cXNVTU9xQUFXS0lzc09pTHFkaGVlWTNYVG54S0FkTXRLd2Ewc09VMlpmUDJGdDhuN0tJMjdacnRxVXNaVUNHTGk0Y0dCd3pzcFlHUnBnZEt5QWxJdGpLYjI5bHFaekRWUUtDOUlVYTQ4L0ZVMmRnWVFTNG85WHJ1NjFOeGYrTWFxRDNpNXNHcGVmZE5NQlBRN0VPL3QxRXl3V3l6dWp3LzZNdXJ4eWpiRUpCbmluSS9GOC9rdzJIbWExNU5aTTlsWFlXdlFYWWtiTStZTzRLT21LY3VNZ2N2TmpwVjd2dXlhRVlwUGo0RFlyb3dZS0h5a3Ewcm9HMTNKN3RabUNCd2hXN2RTRHFWbW03UmU5dmFyekplcm9xWDArUTNLeVZQWkhXL3BXUXVRZm0zc0xJTWVQUE1mZ2pOSXhaMGMwazN5eEVjVkkydEErcUdVL0ViZDRxM2dTR0tFeGJZb21XNmdMQWdpVFEwaVJxVEJaTGVMejZqQ2dEc0N1dDY4ai9nazBLNmZDa0QiLCJtYWMiOiJkYmIwNzI2OWZjN2Y5YzE3ODk0ZjcxMGY5NjA4MWQ0NzNhNDJiYTcxOWYzYzYyYzUxNTFiZWQ5NmM0NzdmNTY1IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 05:08:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZ0MWN0ZzZ5ZWRDWDF3dnRVSUthbGc9PSIsInZhbHVlIjoiYUExRnFWcGZzclFNU3hBWk9kUHhtd1NQTDVNNk9GZjY3VGozMjM0c0liR1VqUjlQWjJCa29vVEFJZmpZY1NlbUE1eVVrVUQraW1Cd09VdXFFWnBSMWlrdEtDU2JqWHNaYUMyNWwxSHVGUFVpZ2U5NytmQkpUWEoyMU9lSzRBTGxXc052blVNUVFFODBqeHRCcnNKT2JXaGVHSEM0S3VDc3FPVnVXUXJldlRkUk9WSzB6WE9nc2dnalFHZjBoYlp1U1lRakJtd2hVNzFrMThHckxsZWlyNzBSVm5FL2NZOWRzRjVYTHdqV2pKMzl6TTlzUUg4bG9VWFBsREk2M0NPM2tNTTRFK09KamIwS2NQd3o0emhuNExtdENTZG5SRVRDc0tQeTZ2WDFxYU1PYmlBRDE3clMwZDNKWVlGTUwwVHNnQTd3VkZSRjRsZkpNOVFYeUt2NWZGMUdtb2NyNEZBMU5UcG5uK2gyTnpYbkNCRmVveGNiUVBzOG9XNG5FcVNoWlJsRkptaTFFdWxtNTE5L3FNdDJ3cUNOS1hEUXEyd1BMWVJHY0xtd0RDUmdReXkzZ3ZPOEtDcjNzaHp2VU9jeE42VnYzaWgyZm9rUkQ3ZEFrY3NzZDhhNmE3Kzl4K3dhMDBMeWg3aGNEREJnNktJcmZvK3hpSGVHaVNLVzcxbmwiLCJtYWMiOiJiMjFhZTE5NDE4MjIxMDI4Y2JjYjc4MWE0MzA2YWRlMjIzMjcyZWFiZmM5MTc4ZTRmZjEwNDNkNjcxMGUwZjkyIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 05:08:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlEzbHFnSmxJT2kxNW5sVEVKN01Cb0E9PSIsInZhbHVlIjoibzdTZDBZWHdWTTR0RXRGaytWVWZ0WlhrcVQ5R0FVR2R0djRuQWo2UWtRamoyQXRpNFpMbG9aaDZTOUd2SC83MUxmNWhJdTMxUVlwVG5uaCswQ0tEL1MxL29nOGZlcy9HRlZLNzBXYUMwZ2VQeU04RzV5ZGNVUEV4cXNVTU9xQUFXS0lzc09pTHFkaGVlWTNYVG54S0FkTXRLd2Ewc09VMlpmUDJGdDhuN0tJMjdacnRxVXNaVUNHTGk0Y0dCd3pzcFlHUnBnZEt5QWxJdGpLYjI5bHFaekRWUUtDOUlVYTQ4L0ZVMmRnWVFTNG85WHJ1NjFOeGYrTWFxRDNpNXNHcGVmZE5NQlBRN0VPL3QxRXl3V3l6dWp3LzZNdXJ4eWpiRUpCbmluSS9GOC9rdzJIbWExNU5aTTlsWFlXdlFYWWtiTStZTzRLT21LY3VNZ2N2TmpwVjd2dXlhRVlwUGo0RFlyb3dZS0h5a3Ewcm9HMTNKN3RabUNCd2hXN2RTRHFWbW03UmU5dmFyekplcm9xWDArUTNLeVZQWkhXL3BXUXVRZm0zc0xJTWVQUE1mZ2pOSXhaMGMwazN5eEVjVkkydEErcUdVL0ViZDRxM2dTR0tFeGJZb21XNmdMQWdpVFEwaVJxVEJaTGVMejZqQ2dEc0N1dDY4ai9nazBLNmZDa0QiLCJtYWMiOiJkYmIwNzI2OWZjN2Y5YzE3ODk0ZjcxMGY5NjA4MWQ0NzNhNDJiYTcxOWYzYzYyYzUxNTFiZWQ5NmM0NzdmNTY1IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 05:08:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294788873\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}}