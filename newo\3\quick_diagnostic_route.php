<?php
// أضف هذا الـ route إلى ملف routes/web.php للتشخيص السريع

Route::get('quick-diagnosis', function() {
    $user = Auth::user();
    
    $diagnosis = [
        'timestamp' => now(),
        'user_info' => [
            'id' => $user->id,
            'name' => $user->name,
            'warehouse_id' => $user->warehouse_id,
            'is_sale_session_new' => $user->is_sale_session_new ?? 'FIELD_NOT_EXISTS',
            'type' => $user->type,
        ],
        'permissions' => [
            'manage_pos' => $user->can('manage pos'),
            'show_financial_record' => $user->can('show financial record'),
        ],
        'database_checks' => [
            'users_table_has_field' => Schema::hasColumn('users', 'is_sale_session_new'),
            'shifts_table_exists' => Schema::hasTable('shifts'),
            'financial_records_table_exists' => Schema::hasTable('financial_records'),
        ],
        'file_checks' => [
            'controller_exists' => class_exists('App\Http\Controllers\FinancialRecordController'),
            'view_exists' => view()->exists('pos.financial_record.opening-balance'),
            'service_exists' => class_exists('App\Services\FinancialRecordService'),
        ],
        'route_checks' => [
            'opening_balance_route' => Route::has('pos.financial.opening.balance'),
            'financial_record_route' => Route::has('pos.financial.record'),
        ],
        'calculated_values' => [
            'is_sale_session_new_condition' => $user->is_sale_session_new && $user->can('manage pos'),
            'should_show_opening_balance' => $user->can('manage pos') && ($user->is_sale_session_new ?? false),
        ]
    ];
    
    return response()->json($diagnosis, 200, [], JSON_PRETTY_PRINT);
})->middleware(['auth'])->name('quick.diagnosis');

// أضف أيضاً route لاختبار opening balance مباشرة
Route::get('test-opening-balance', function() {
    try {
        $user = Auth::user();
        
        if (!$user->warehouse_id) {
            return response()->json(['error' => 'No warehouse assigned'], 400);
        }
        
        if (!$user->can('manage pos')) {
            return response()->json(['error' => 'No manage pos permission'], 403);
        }
        
        if (!($user->is_sale_session_new ?? false)) {
            return response()->json(['error' => 'is_sale_session_new is false or not exists'], 400);
        }
        
        if (!view()->exists('pos.financial_record.opening-balance')) {
            return response()->json(['error' => 'View file not found'], 404);
        }
        
        return view('pos.financial_record.opening-balance');
        
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->middleware(['auth'])->name('test.opening.balance');
