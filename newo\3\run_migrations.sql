-- إن<PERSON>اء جدول أوامر الاستلام
CREATE TABLE IF NOT EXISTS `receipt_orders` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_number` varchar(255) NOT NULL,
    `order_type` enum('استلام بضاعة','نقل بضاعة','أمر إخراج') NOT NULL,
    `vendor_id` bigint(20) UNSIGNED DEFAULT NULL,
    `warehouse_id` bigint(20) UNSIGNED NOT NULL,
    `from_warehouse_id` bigint(20) UNSIGNED DEFAULT NULL,
    `invoice_number` varchar(255) DEFAULT NULL,
    `invoice_total` decimal(15,2) DEFAULT NULL,
    `invoice_date` date DEFAULT NULL,
    `has_return` tinyint(1) NOT NULL DEFAULT 0,
    `total_products` int(11) NOT NULL DEFAULT 0,
    `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
    `notes` text DEFAULT NULL,
    `exit_reason` enum('فقدان','منتهي الصلاحية','تلف/خراب','بيع بالتجزئة') DEFAULT NULL,
    `exit_date` date DEFAULT NULL,
    `responsible_person` varchar(255) DEFAULT NULL,
    `created_by` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `receipt_orders_order_number_unique` (`order_number`),
    KEY `receipt_orders_vendor_id_foreign` (`vendor_id`),
    KEY `receipt_orders_warehouse_id_foreign` (`warehouse_id`),
    KEY `receipt_orders_from_warehouse_id_foreign` (`from_warehouse_id`),
    KEY `receipt_orders_created_by_foreign` (`created_by`),
    KEY `receipt_orders_order_type_created_by_index` (`order_type`,`created_by`),
    KEY `receipt_orders_warehouse_id_created_by_index` (`warehouse_id`,`created_by`),
    KEY `receipt_orders_vendor_id_created_by_index` (`vendor_id`,`created_by`),
    KEY `receipt_orders_invoice_date_created_by_index` (`invoice_date`,`created_by`),
    KEY `receipt_orders_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول منتجات أوامر الاستلام
CREATE TABLE IF NOT EXISTS `receipt_order_products` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `receipt_order_id` bigint(20) UNSIGNED NOT NULL,
    `product_id` bigint(20) UNSIGNED NOT NULL,
    `quantity` decimal(15,2) NOT NULL,
    `unit_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `total_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `expiry_date` date DEFAULT NULL,
    `is_return` tinyint(1) NOT NULL DEFAULT 0,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `receipt_order_products_receipt_order_id_foreign` (`receipt_order_id`),
    KEY `receipt_order_products_product_id_foreign` (`product_id`),
    KEY `receipt_order_products_receipt_order_id_product_id_index` (`receipt_order_id`,`product_id`),
    KEY `receipt_order_products_product_id_is_return_index` (`product_id`,`is_return`),
    KEY `receipt_order_products_expiry_date_index` (`expiry_date`),
    KEY `receipt_order_products_quantity_index` (`quantity`),
    KEY `receipt_order_products_total_cost_index` (`total_cost`),
    KEY `receipt_order_products_created_at_index` (`created_at`),
    UNIQUE KEY `unique_receipt_product_expiry` (`receipt_order_id`,`product_id`,`expiry_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة المفاتيح الخارجية (Foreign Keys)
-- ملاحظة: قم بتشغيل هذه الأوامر فقط إذا كانت الجداول المرجعية موجودة

-- للجدول الأول
ALTER TABLE `receipt_orders` 
ADD CONSTRAINT `receipt_orders_vendor_id_foreign` 
FOREIGN KEY (`vendor_id`) REFERENCES `venders` (`id`) ON DELETE SET NULL;

ALTER TABLE `receipt_orders` 
ADD CONSTRAINT `receipt_orders_warehouse_id_foreign` 
FOREIGN KEY (`warehouse_id`) REFERENCES `warehouses` (`id`) ON DELETE CASCADE;

ALTER TABLE `receipt_orders` 
ADD CONSTRAINT `receipt_orders_from_warehouse_id_foreign` 
FOREIGN KEY (`from_warehouse_id`) REFERENCES `warehouses` (`id`) ON DELETE SET NULL;

ALTER TABLE `receipt_orders` 
ADD CONSTRAINT `receipt_orders_created_by_foreign` 
FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- للجدول الثاني
ALTER TABLE `receipt_order_products` 
ADD CONSTRAINT `receipt_order_products_receipt_order_id_foreign` 
FOREIGN KEY (`receipt_order_id`) REFERENCES `receipt_orders` (`id`) ON DELETE CASCADE;

ALTER TABLE `receipt_order_products` 
ADD CONSTRAINT `receipt_order_products_product_id_foreign` 
FOREIGN KEY (`product_id`) REFERENCES `product_services` (`id`) ON DELETE CASCADE;
