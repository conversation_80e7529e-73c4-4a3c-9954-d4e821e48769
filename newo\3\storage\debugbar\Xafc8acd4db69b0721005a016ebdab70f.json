{"__meta": {"id": "Xafc8acd4db69b0721005a016ebdab70f", "datetime": "2025-06-14 02:51:25", "utime": **********.509083, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.40005, "end": **********.509113, "duration": 1.****************, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": **********.40005, "relative_start": 0, "end": **********.342209, "relative_end": **********.342209, "duration": 0.****************, "duration_str": "942ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.342228, "relative_start": 0.****************, "end": **********.509116, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "167ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023790000000000002, "accumulated_duration_str": "23.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.416582, "duration": 0.020460000000000002, "duration_str": "20.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.003}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.457424, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.003, "width_percent": 7.524}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.491837, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.527, "width_percent": 6.473}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749869483217%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhKQWx3blpYUk56c09wOEhSaHRuUVE9PSIsInZhbHVlIjoiWVZsdGpSUWJsYXdrQ0lwTnpyYVZCNlNFUWpiRGJTc0hZa1U0aVJGSW5RNW82cksxcGhJZVFwa3k5WXNOV3VEcGRrU1FlQnFZWW1zUkh1OHVPc0QvMVRQZkZ6bFBWaGdXK3NEQmR0RWFiMHlSWFpIcmFURnZkS3dBYkp0V2NzWHMxWFIzY2gwcVdrNlpoVnlkNXh1SWZGamJsZFcxWVhEZysremZyem1meUdESnBYSko3R09mM3NvcTlGU2w0aFJkSkNWamp6aFdVeGZUdnVyU2JIWERMbzNwOStzWW5lR3hLL1RnYlhmVG5HUWlxS2lBRTd4aUt6K0hhQUhIK3k0N1A3UTA2K2UvK0I3Z1c1clRBWEhKdmN3NnpFWVNBL25xc2x3NUMybUx2Q3JIMXI5TlArYnRRVjMyamI4VUN1cGdYUHVDVDNERWl5SXVSUXd0SVErZnE3bXhzVkkxWmxydEZmQVNhV0NDL29VSmg1b3VaRUdOZUxNQmt2OEFSN3Fmb3FpcXpmcWRqS0ZrQS9OZUg5VnNmSXcxTEhpTUs1UExtV2tlV3ByN3pMYWlla2t3eC9ZTUhMdWJsQnBtYjk1K3pWVWwrUXRTMzdpS3pRQzhNZmk4bjN4Ry84cWpOYWNFalAyY0ZvS2w3b2NJVHQ2eGdsOFFlWXZ1YVN1R1h0UkUiLCJtYWMiOiI1MDk5YmUzMzU2OWI2ZTZkNzNhOTI3Mjg3ZDdhNWJhNWI3Y2M3NjQ2YmRlMmNmOTg3ZDM1OTU3NWQ4NTYwYTQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRhRUdDSTNFbXRKelBnVXUyUjJOU3c9PSIsInZhbHVlIjoiVFNYdkRNNy9IVUxOdFJVUDFPVGdwZzluOGtBVEVBZi83UWVOdnE2TXNVbHA2bGhjdWNYYjVRM0ZWM0lpWkdiUlJCN3M4RWtTQmsrRWs1RSsxQzFpMEZuR2RDbk5kamFHL0pEenNmbm51Z002eFNlS1FFeUZGWTFDamlKUDlSWE4yM0N4VWhqY3BBay9ra044bVpDS3orOEwxVTlMY3oxY1FvcTd6S1JlazR1YUtLNzNYMmxzRHkrUjhGWFZTVndGRXVkTk03bENQWHpicis0YWNxdkUyVzdWbWNpNGoyYWZRNndSNFBpSE8vd2dBSlRVQ1RNR2hudGk4d2JMb1pCeWVRVVcrcW5DdjFENGt2cklRWGhwczd2Wm9DaXhIUldaU3BCZ0hBWFJBSVNycVhDSU1rbXVZS0RaOWFGTE9sb1l0YU1RYm1nOTBNS3U3dWlrNE5OdEhZQzFoK211M3Z6K1J3L0hBSWhEQXc1QWo3aHZPMWsvbHNrcDk4TkxKRzNNaWRBY290OVhiUVNDd25kVlV5L21mYlBwTmY5amRMSjlNM2s5b2JSeVlSenp2V21Pb011MGpPU0ovZlljaWk2WFVjeWFIVitjRGQwaHZ6ek03OERCeFNRbjZpNWxqWXpWdDQ5UlI3TkZ3ek9oNkR0NHFJcFVqOGE4dndFQnQxRHIiLCJtYWMiOiJlNjA5OTUzY2MxZjUyMDI3NjZmZjE3ZmMxZmM2YjViYmQwNjhhY2Y4MGQzM2ZmOTYyM2MxOWVlNTkzYjgzOGUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1990322516 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t6clpBhZQEC0tcIVAqApy3fwEvNxCUuY7Y8VEjFB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1990322516\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-10625547 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 02:51:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNKMUsyOGtvUXdJeTVHbHAxK0xON1E9PSIsInZhbHVlIjoiTjVyUWpmM2d5c0kvY1pDQUVERm1XQVN3WmkvM1NteCtYZEMvUUJ3ekxTb1crM2thUlZQbDE0RlFzdDNnQkNiYnl4VURxZzQyUUhTbjdZTmRtNlNYbU5SUDRuMzVoL0RDNEhBNmVLZDQvTno0K0RqTy9ISlc3aElHREZTR21vdVZZNGhib3lJbVljRHVWRWdCUE1LQlNMaDN4MlNDT0E2L1N6dDAzVCthVnZWQTZWKzlUbWRwYmxwZm05Zm9NY1FVSHozdUNYbUxGUUVWM0ZxRTg0ZU9zQ0h0VU8xTEtpSVhuZG1Yb1c4aGhTOSt0MEwyL0ZSYmlRT0FaSGJSZmdLOGxhZU1UOFpaa2M4RXNSUUlscnNmTTV2eVd1cXExd085UG02cXR3dnFjc2JmOGNwSGthRnUzV3M5dG4yYUlqQkllRUIxeUdjN3VMNnNFTzZ1TG1KRzY0eURnNVBBbFhOVTdRbGhLdDZqcVFzWHRGbnptKzNtY0dDNlRWMVdLZFpTTWVQUy9YcTl0Y0RzbkJGeGY3NDZqSjRpYWZjYThZdDBBMTZINmtkeSs0SDhNSFhXT2RzOVlJMm9ScGhHVWZ4MG9zblNXb3gwOUp5bkdIUSt5VnprUWhmSi9NemVFSnpCVUJkTmFqZjRrT3UrVGRDcXVrWEVCSGQvVnNPY29yR00iLCJtYWMiOiJlZmQwNmVlMGU2MzYxNWM1YjU3YTIxOWQxNDQ4M2EwNjMzNTEyYTI4MWQwMDdkOGZhNDZlYTkwM2VmZTM4NDRiIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkY3aXd1UnNNUUF5UnlhVFdqTTVQc1E9PSIsInZhbHVlIjoiZTRHbXpRQ2pZcnNxK3cxbGZ5WEtDUXJBZmJIYUxHc005S1ZDdTlHVzVrWithNWwyRnR3U0kxVmY4L3ZSYnBJR0QyQVRxU2hNOU44eTFxVHV1d251OGl6MjhEQmt1ci9taGRHeGtNUSs5NlEwUFVXdFJST29DL2tIdU9HN3NmMlVuSnhGVzBGdmJpdHdyVENPaVZYTmhocnhIM0dUdUNxR2JBUTl3Z1JLc0tGemE0cmlNQmI4OWNOTTRVM2hBOWtXZTlRQ2ltQnhWVmZZOFdEK2pFNXlERFFzWm13Z2s4VHdHYis2ZjJ0TVZzYnM1VklSUFViZFRyYS9iVEIrdDd5NzBXeHFyZDlFb2Zjdk1MZEhLcE9CUTg2WjF3TURIZWhoMFBTL0RLTk9iTzZzWENlUUpZV1oveFR3UHBURUVyZlpCYklmQno0WjV2MWhjNGluWmd4RERheFBwbGFKYmM0a0lDZnY1emZTYTJSYnpOcFE2MnVBeWJTcnE1a0NBWFlaMXcxQ3AyWW1vSVlha3lQRFNvcWVnNDZkVUZuejZaU1BFTWdCL2cvcWhLV3hNbnRjVlBrQ0pLT1QyaGJtWlpnNThUSCtOZUlsUzFIZWtjejVaZnN3TStGWCtaVWVZV2lnU0ZSTEt6MXZuRkRVQ2ZYU3pHZ0R5VjdYbXM1Y0U5Sk4iLCJtYWMiOiJhZjkwZTY0YTc1NTZmOGE0NGY5YWYyZjdmOTQwZjk4NDQxZGEzMGY1YzRjNGEzMmRhYmVlMjIxMDQ1YjQwMjhiIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNKMUsyOGtvUXdJeTVHbHAxK0xON1E9PSIsInZhbHVlIjoiTjVyUWpmM2d5c0kvY1pDQUVERm1XQVN3WmkvM1NteCtYZEMvUUJ3ekxTb1crM2thUlZQbDE0RlFzdDNnQkNiYnl4VURxZzQyUUhTbjdZTmRtNlNYbU5SUDRuMzVoL0RDNEhBNmVLZDQvTno0K0RqTy9ISlc3aElHREZTR21vdVZZNGhib3lJbVljRHVWRWdCUE1LQlNMaDN4MlNDT0E2L1N6dDAzVCthVnZWQTZWKzlUbWRwYmxwZm05Zm9NY1FVSHozdUNYbUxGUUVWM0ZxRTg0ZU9zQ0h0VU8xTEtpSVhuZG1Yb1c4aGhTOSt0MEwyL0ZSYmlRT0FaSGJSZmdLOGxhZU1UOFpaa2M4RXNSUUlscnNmTTV2eVd1cXExd085UG02cXR3dnFjc2JmOGNwSGthRnUzV3M5dG4yYUlqQkllRUIxeUdjN3VMNnNFTzZ1TG1KRzY0eURnNVBBbFhOVTdRbGhLdDZqcVFzWHRGbnptKzNtY0dDNlRWMVdLZFpTTWVQUy9YcTl0Y0RzbkJGeGY3NDZqSjRpYWZjYThZdDBBMTZINmtkeSs0SDhNSFhXT2RzOVlJMm9ScGhHVWZ4MG9zblNXb3gwOUp5bkdIUSt5VnprUWhmSi9NemVFSnpCVUJkTmFqZjRrT3UrVGRDcXVrWEVCSGQvVnNPY29yR00iLCJtYWMiOiJlZmQwNmVlMGU2MzYxNWM1YjU3YTIxOWQxNDQ4M2EwNjMzNTEyYTI4MWQwMDdkOGZhNDZlYTkwM2VmZTM4NDRiIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkY3aXd1UnNNUUF5UnlhVFdqTTVQc1E9PSIsInZhbHVlIjoiZTRHbXpRQ2pZcnNxK3cxbGZ5WEtDUXJBZmJIYUxHc005S1ZDdTlHVzVrWithNWwyRnR3U0kxVmY4L3ZSYnBJR0QyQVRxU2hNOU44eTFxVHV1d251OGl6MjhEQmt1ci9taGRHeGtNUSs5NlEwUFVXdFJST29DL2tIdU9HN3NmMlVuSnhGVzBGdmJpdHdyVENPaVZYTmhocnhIM0dUdUNxR2JBUTl3Z1JLc0tGemE0cmlNQmI4OWNOTTRVM2hBOWtXZTlRQ2ltQnhWVmZZOFdEK2pFNXlERFFzWm13Z2s4VHdHYis2ZjJ0TVZzYnM1VklSUFViZFRyYS9iVEIrdDd5NzBXeHFyZDlFb2Zjdk1MZEhLcE9CUTg2WjF3TURIZWhoMFBTL0RLTk9iTzZzWENlUUpZV1oveFR3UHBURUVyZlpCYklmQno0WjV2MWhjNGluWmd4RERheFBwbGFKYmM0a0lDZnY1emZTYTJSYnpOcFE2MnVBeWJTcnE1a0NBWFlaMXcxQ3AyWW1vSVlha3lQRFNvcWVnNDZkVUZuejZaU1BFTWdCL2cvcWhLV3hNbnRjVlBrQ0pLT1QyaGJtWlpnNThUSCtOZUlsUzFIZWtjejVaZnN3TStGWCtaVWVZV2lnU0ZSTEt6MXZuRkRVQ2ZYU3pHZ0R5VjdYbXM1Y0U5Sk4iLCJtYWMiOiJhZjkwZTY0YTc1NTZmOGE0NGY5YWYyZjdmOTQwZjk4NDQxZGEzMGY1YzRjNGEzMmRhYmVlMjIxMDQ1YjQwMjhiIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10625547\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-220948812 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220948812\", {\"maxDepth\":0})</script>\n"}}