<?php
// فحص سريع لمشكلة عدم ظهور شجرة الحسابات
echo "<h1>فحص سريع - لماذا لا تظهر شجرة الحسابات؟</h1>";

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'ty';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>1. فحص وجود البيانات</h2>";

// فحص الجداول والبيانات
$tables = [
    'chart_of_account_types' => 'أنواع الحسابات',
    'chart_of_account_sub_types' => 'الأنواع الفرعية', 
    'chart_of_accounts' => 'الحسابات'
];

foreach ($tables as $table => $name) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count > 0) {
            echo "<p style='color: green;'>✓ $name: $count سجل</p>";
        } else {
            echo "<p style='color: red;'>✗ $name: فارغ (0 سجل)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في فحص $name: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>2. فحص البيانات حسب created_by</h2>";

// فحص البيانات حسب created_by
try {
    $stmt = $pdo->query("SELECT DISTINCT created_by, COUNT(*) as count FROM chart_of_account_types GROUP BY created_by");
    $typesByUser = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>أنواع الحسابات حسب المستخدم:</h3>";
    foreach ($typesByUser as $row) {
        echo "<p>المستخدم {$row['created_by']}: {$row['count']} نوع</p>";
    }
    
    $stmt = $pdo->query("SELECT DISTINCT created_by, COUNT(*) as count FROM chart_of_accounts GROUP BY created_by");
    $accountsByUser = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>الحسابات حسب المستخدم:</h3>";
    foreach ($accountsByUser as $row) {
        echo "<p>المستخدم {$row['created_by']}: {$row['count']} حساب</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>3. فحص المستخدمين</h2>";

try {
    $stmt = $pdo->query("SELECT id, name, email, created_by FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>Created By</th><th>Creator ID المتوقع</th></tr>";
    
    foreach ($users as $user) {
        $expectedCreatorId = $user['created_by'] ?? $user['id'];
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['created_by']}</td>";
        echo "<td style='background-color: #ffffcc;'><strong>$expectedCreatorId</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص المستخدمين: " . $e->getMessage() . "</p>";
}

echo "<h2>4. التشخيص والحل</h2>";

// تحديد المشكلة والحل
$stmt = $pdo->query("SELECT COUNT(*) as count FROM chart_of_account_types");
$typesCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

$stmt = $pdo->query("SELECT COUNT(*) as count FROM chart_of_accounts");
$accountsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

echo "<div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #dc3545;'>";

if ($typesCount == 0 && $accountsCount == 0) {
    echo "<h3 style='color: #dc3545;'>المشكلة: لا توجد بيانات في قاعدة البيانات</h3>";
    echo "<p><strong>الحل:</strong></p>";
    echo "<ol>";
    echo "<li><a href='seed_chart_accounts_direct.php' style='background-color: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>إنشاء البيانات الأساسية</a></li>";
    echo "</ol>";
} elseif ($typesCount > 0 && $accountsCount > 0) {
    echo "<h3 style='color: #ffc107;'>المشكلة: البيانات موجودة ولكن لا تظهر</h3>";
    echo "<p><strong>الأسباب المحتملة:</strong></p>";
    echo "<ol>";
    echo "<li>عدم تسجيل الدخول</li>";
    echo "<li>عدم وجود صلاحية 'manage chart of account'</li>";
    echo "<li>عدم تطابق created_by مع المستخدم الحالي</li>";
    echo "<li>خطأ في الكود أو العرض</li>";
    echo "</ol>";
    
    echo "<p><strong>الحلول:</strong></p>";
    echo "<ol>";
    echo "<li><a href='fix_created_by_values.php' style='background-color: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>إصلاح قيم created_by</a></li>";
    echo "<li><a href='http://localhost/chart-of-account' target='_blank' style='background-color: #17a2b8; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>اختبار الصفحة</a></li>";
    echo "</ol>";
} else {
    echo "<h3 style='color: #ffc107;'>المشكلة: بيانات ناقصة</h3>";
    echo "<p>أنواع الحسابات: $typesCount | الحسابات: $accountsCount</p>";
    echo "<p><strong>الحل:</strong></p>";
    echo "<ol>";
    echo "<li><a href='seed_chart_accounts_direct.php' style='background-color: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>إكمال البيانات المفقودة</a></li>";
    echo "</ol>";
}

echo "</div>";

echo "<h2>5. اختبار سريع للصفحة</h2>";
echo "<p><a href='http://localhost/chart-of-account' target='_blank' style='background-color: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 16px;'>🔗 فتح صفحة شجرة الحسابات</a></p>";

echo "<h2>6. أدوات إضافية</h2>";
echo "<p>";
echo "<a href='debug_chart_simple.php' style='background-color: #6c757d; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>فحص تفصيلي</a>";
echo "<a href='comprehensive_chart_diagnosis.php' style='background-color: #fd7e14; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>فحص شامل</a>";
echo "</p>";
?>
