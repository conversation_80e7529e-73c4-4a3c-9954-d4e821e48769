<?php
// إضافة هذا الكود إلى ملف resources/views/partials/admin/menu.blade.php
// في بداية الملف إضافة هذا المتغير:

$is_sale_session_new = Auth::user()['is_sale_session_new'] && Auth::user()->can('manage pos');

// ثم في قسم القائمة إضافة:

@can('show financial record')
    <li class="dash-item {{ Request::route()->getName() == 'pos.financial.record' ? ' active' : '' }}" >
        <a class="dash-link" data-fmodel="{{$is_sale_session_new ? 'true' : ''}}" data-title="{{ __('Financial Record') }}" data-url="{{ route('pos.financial.record') }}"
           href="{{!$is_sale_session_new ? route('pos.financial.record') : '#'}}">{{ __('Financial Record') }}</a>
    </li>
@endcan

// وفي نهاية الملف إضافة JavaScript:

@section('custom-js')
    <script>
        $(document).ready(function () {
            $('a[data-fmodel="true"]').click(function () {
                console.log("URL:",$(this).data("url"));
                var data = {};
                var title1 = $(this).data("title");
                var title2 = $(this).data("bs-original-title");
                var title3 = $(this).data("original-title");
                var title = (title1 != undefined) ? title1 : title2;
                var title=(title != undefined) ? title : title3;

                $('#commonModal .modal-dialog').removeClass('modal-sm modal-md modal-lg modal-xl modal-xxl');
                var size = ($(this).data('size') == '') ? 'md' : $(this).data('size');

                var url = $(this).data('url');
                $("#commonModal .modal-title").html(title);
                $("#commonModal .modal-dialog").addClass('modal-' + size);

                if ($('#vc_name_hidden').length > 0) {
                    data['vc_name'] = $('#vc_name_hidden').val();
                }
                if ($('#warehouse_name_hidden').length > 0) {
                    data['warehouse_name'] = $('#warehouse_name_hidden').val();
                }
                if ($('#discount_hidden').length > 0) {
                    data['discount'] = $('#discount_hidden').val();
                }
                if ($('#quotation_id').length > 0) {
                    data['quotation_id'] = $('#quotation_id').val();
                }
                $.ajax({
                    url: url,
                    data: data,
                    success: function (data) {
                        if (data && data.trim() !== '') {
                            $('#commonModal .modal-body').html(data);
                            $("#commonModal").modal('show');
                            taskCheckbox();
                            common_bind("#commonModal");
                            validation();
                            commonLoader();
                        } else {
                            show_toastr('Error', 'Empty response received', 'error');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr, status, error);
                        var errorMessage = 'An error occurred';
                        
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        
                        show_toastr('Error', errorMessage, 'error');
                    }
                });
            });
        });
    </script>
@endsection
