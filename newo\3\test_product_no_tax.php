<?php
// اختبار إنشاء منتج بدون ضرائب
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\ProductServiceUnit;
use App\Models\ChartOfAccount;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Controllers\ProductServiceController;

echo "<h1>اختبار إنشاء منتج بدون ضرائب</h1>";

try {
    // البحث عن مستخدم
    $user = User::where('type', 'company')->first();
    if (!$user) {
        echo "<p style='color: red;'>❌ لا يوجد مستخدم للاختبار</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ المستخدم: {$user->name} (ID: {$user->id})</p>";
    
    // تسجيل دخول المستخدم
    Auth::login($user);
    
    // التحقق من البيانات المطلوبة
    $category = DB::table('product_service_categories')
        ->where('type', 'product & service')
        ->where('created_by', $user->creatorId())
        ->first();
    
    if (!$category) {
        echo "<p style='color: orange;'>⚠️ إنشاء فئة تجريبية...</p>";
        $categoryId = DB::table('product_service_categories')->insertGetId([
            'name' => 'فئة تجريبية',
            'type' => 'product & service',
            'color' => '#007bff',
            'created_by' => $user->creatorId(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $category = (object)['id' => $categoryId, 'name' => 'فئة تجريبية'];
        echo "<p style='color: green;'>✅ تم إنشاء فئة تجريبية (ID: {$categoryId})</p>";
    }
    
    $unit = DB::table('product_service_units')
        ->where('created_by', $user->creatorId())
        ->first();
    
    if (!$unit) {
        echo "<p style='color: orange;'>⚠️ إنشاء وحدة تجريبية...</p>";
        $unitId = DB::table('product_service_units')->insertGetId([
            'name' => 'قطعة',
            'created_by' => $user->creatorId(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $unit = (object)['id' => $unitId, 'name' => 'قطعة'];
        echo "<p style='color: green;'>✅ تم إنشاء وحدة تجريبية (ID: {$unitId})</p>";
    }
    
    // البحث عن حسابات
    $incomeAccount = DB::table('chart_of_accounts')
        ->leftJoin('chart_of_account_types', 'chart_of_account_types.id', '=', 'chart_of_accounts.type')
        ->where('chart_of_account_types.name', 'income')
        ->where('chart_of_accounts.created_by', $user->creatorId())
        ->select('chart_of_accounts.*')
        ->first();
    
    $expenseAccount = DB::table('chart_of_accounts')
        ->leftJoin('chart_of_account_types', 'chart_of_account_types.id', '=', 'chart_of_accounts.type')
        ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
        ->where('chart_of_accounts.created_by', $user->creatorId())
        ->select('chart_of_accounts.*')
        ->first();
    
    if (!$incomeAccount || !$expenseAccount) {
        echo "<p style='color: red;'>❌ لا توجد حسابات إيرادات أو مصروفات</p>";
        echo "<p>يرجى إنشاء الحسابات من: <a href='/chart-of-account/create'>/chart-of-account/create</a></p>";
        exit;
    }
    
    echo "<p>✅ حساب الإيرادات: {$incomeAccount->name} (ID: {$incomeAccount->id})</p>";
    echo "<p>✅ حساب المصروفات: {$expenseAccount->name} (ID: {$expenseAccount->id})</p>";
    
    // محاكاة طلب HTTP
    $testSku = 'TEST-NO-TAX-' . time();
    
    echo "<h2>اختبار إنشاء منتج بدون ضرائب:</h2>";
    echo "<p>SKU: {$testSku}</p>";
    
    // إنشاء طلب مزيف
    $requestData = [
        'name' => 'منتج بدون ضرائب - ' . date('Y-m-d H:i:s'),
        'sku' => $testSku,
        'sale_price' => 150.00,
        'purchase_price' => 120.00,
        'quantity' => 5,
        'type' => 'product',
        'category_id' => $category->id,
        'unit_id' => $unit->id,
        'sale_chartaccount_id' => $incomeAccount->id,
        'expense_chartaccount_id' => $expenseAccount->id,
        'tax_id' => [], // مصفوفة فارغة للضرائب
        'description' => 'منتج تجريبي بدون ضرائب',
        '_token' => csrf_token()
    ];
    
    echo "<h3>بيانات الطلب:</h3>";
    echo "<pre>" . print_r($requestData, true) . "</pre>";
    
    // إنشاء كائن Request
    $request = new Request();
    $request->replace($requestData);
    $request->setMethod('POST');
    
    // استدعاء الكنترولر
    $controller = new ProductServiceController();
    
    echo "<h3>استدعاء الكنترولر...</h3>";
    
    $response = $controller->store($request);
    
    echo "<h3>استجابة الكنترولر:</h3>";
    
    if ($response instanceof \Illuminate\Http\JsonResponse) {
        $responseData = $response->getData(true);
        echo "<pre>" . print_r($responseData, true) . "</pre>";
        
        if (isset($responseData['success']) && $responseData['success']) {
            echo "<p style='color: green; font-size: 18px;'>🎉 تم إنشاء المنتج بنجاح عبر الكنترولر!</p>";
            
            if (isset($responseData['product'])) {
                $product = $responseData['product'];
                echo "<h4>تفاصيل المنتج:</h4>";
                echo "<ul>";
                echo "<li><strong>ID:</strong> {$product['id']}</li>";
                echo "<li><strong>الاسم:</strong> {$product['name']}</li>";
                echo "<li><strong>SKU:</strong> {$product['sku']}</li>";
                echo "<li><strong>سعر البيع:</strong> {$product['sale_price']}</li>";
                echo "<li><strong>الضرائب:</strong> " . ($product['tax_id'] ?: 'لا توجد') . "</li>";
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء المنتج: " . ($responseData['message'] ?? 'خطأ غير معروف') . "</p>";
        }
    } else {
        echo "<p>نوع الاستجابة: " . get_class($response) . "</p>";
        echo "<p>محتوى الاستجابة: " . $response->getContent() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='/productservice'>العودة إلى صفحة المنتجات</a></p>";
?>
