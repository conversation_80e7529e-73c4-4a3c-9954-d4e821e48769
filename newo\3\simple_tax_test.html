<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إنشاء منتج بدون ضرائب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="text-center">اختبار إنشاء منتج بدون ضرائب</h4>
                    </div>
                    <div class="card-body">
                        
                        <form id="test-form" action="/productservice" method="POST">
                            <input type="hidden" name="_token" value="">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" value="منتج تجريبي بدون ضرائب" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="sku" class="form-label">الرمز التعريفي (SKU) <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="sku" name="sku" value="" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="sale_price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="sale_price" name="sale_price" value="100" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="purchase_price" class="form-label">سعر الشراء <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="purchase_price" name="purchase_price" value="80" step="0.01" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="category_id" class="form-label">الفئة <span class="text-danger">*</span></label>
                                        <select class="form-control" id="category_id" name="category_id" required>
                                            <option value="">اختر الفئة</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="unit_id" class="form-label">الوحدة <span class="text-danger">*</span></label>
                                        <select class="form-control" id="unit_id" name="unit_id" required>
                                            <option value="">اختر الوحدة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="sale_chartaccount_id" class="form-label">حساب الإيرادات <span class="text-danger">*</span></label>
                                        <select class="form-control" id="sale_chartaccount_id" name="sale_chartaccount_id" required>
                                            <option value="">اختر حساب الإيرادات</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="expense_chartaccount_id" class="form-label">حساب المصروفات <span class="text-danger">*</span></label>
                                        <select class="form-control" id="expense_chartaccount_id" name="expense_chartaccount_id" required>
                                            <option value="">اختر حساب المصروفات</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group mb-3">
                                        <label for="tax_id" class="form-label">الضريبة (اختياري)</label>
                                        <select class="form-control" id="tax_id" name="tax_id[]" multiple>
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </select>
                                        <small class="text-muted">يمكنك ترك هذا الحقل فارغاً</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group mb-3">
                                        <label class="form-label">النوع</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input type="radio" class="form-check-input" id="product" name="type" value="product" checked>
                                                    <label class="form-check-label" for="product">منتج</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input type="radio" class="form-check-input" id="service" name="type" value="service">
                                                    <label class="form-check-label" for="service">خدمة</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row quantity">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="quantity" class="form-label">الكمية <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="quantity" name="quantity" value="10" min="0" step="1" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group mb-3">
                                        <label for="description" class="form-label">الوصف</label>
                                        <textarea class="form-control" id="description" name="description" rows="3">منتج تجريبي بدون ضرائب</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="button" id="load-data-btn" class="btn btn-info me-2">تحميل البيانات</button>
                                <button type="submit" class="btn btn-primary">إنشاء المنتج</button>
                                <button type="button" class="btn btn-secondary" onclick="resetForm()">إعادة تعيين</button>
                            </div>
                        </form>

                        <div id="results" class="mt-4" style="display: none;">
                            <h5>نتائج الاختبار:</h5>
                            <div id="results-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // تعيين SKU تلقائياً
            $('#sku').val('TEST-NO-TAX-' + Date.now());

            // تحميل CSRF token
            $.get('/productservice/create', function(data) {
                var token = $(data).find('input[name="_token"]').val();
                $('input[name="_token"]').val(token);
            });

            // تحميل البيانات
            $('#load-data-btn').click(function() {
                loadFormData();
            });

            // إخفاء/إظهار الكمية حسب النوع
            $('input[name="type"]').change(function() {
                if ($(this).val() === 'product') {
                    $('.quantity').show();
                    $('#quantity').prop('required', true);
                } else {
                    $('.quantity').hide();
                    $('#quantity').prop('required', false);
                }
            });

            // معالجة إرسال النموذج
            $('#test-form').submit(function(e) {
                e.preventDefault();
                
                console.log('Form submission started...');
                
                var formData = new FormData(this);
                
                // عرض البيانات المرسلة
                console.log('Form data:');
                for (var pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                // إرسال الطلب
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Success:', response);
                        $('#results').show();
                        $('#results-content').html('<div class="alert alert-success">تم إنشاء المنتج بنجاح!</div>');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', xhr.responseText);
                        $('#results').show();
                        var errorMsg = 'خطأ غير معروف';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        }
                        $('#results-content').html('<div class="alert alert-danger">خطأ: ' + errorMsg + '</div>');
                    }
                });
            });
        });

        function loadFormData() {
            // تحميل البيانات الأساسية
            $('#category_id').html('<option value="1">فئة تجريبية</option>');
            $('#unit_id').html('<option value="1">قطعة</option>');
            $('#sale_chartaccount_id').html('<option value="1">حساب إيرادات</option>');
            $('#expense_chartaccount_id').html('<option value="1">حساب مصروفات</option>');
            
            // ترك الضرائب فارغة
            $('#tax_id').html('<option value="">لا توجد ضرائب</option>');
        }

        function resetForm() {
            document.getElementById('test-form').reset();
            $('#sku').val('TEST-NO-TAX-' + Date.now());
            $('#results').hide();
        }
    </script>
</body>
</html>
