{"__meta": {"id": "Xcc0e24063dba3f951363e7ff092e2fa9", "datetime": "2025-06-14 02:51:24", "utime": **********.383208, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749869483.022568, "end": **********.383247, "duration": 1.3606789112091064, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1749869483.022568, "relative_start": 0, "end": **********.135825, "relative_end": **********.135825, "duration": 1.1132569313049316, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.135847, "relative_start": 1.113279104232788, "end": **********.383252, "relative_end": 5.0067901611328125e-06, "duration": 0.2474048137664795, "duration_str": "247ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45206824, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.024960000000000003, "accumulated_duration_str": "24.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2278771, "duration": 0.020120000000000002, "duration_str": "20.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.609}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.271664, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.609, "width_percent": 6.571}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.335646, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 87.179, "width_percent": 7.853}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.356059, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.032, "width_percent": 4.968}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-22316368 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-22316368\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-411676441 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-411676441\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1488871780 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488871780\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-947919442 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749869464702%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlhNjdrMllxblRFZm44MEltVjNqdUE9PSIsInZhbHVlIjoiTXlOODJEajM4RnNwWWw3aFpaYk4xcXdLSU9zZnNreVVyM21zRGtqbzI4U2wyb0VQRW1xWGVrVFFabXRvTEVQRWlOWE5tTjVpWVRIK2MrNG1IQ3htL2doTUZRVTR4Sm9DcEpYWFBobHFucENPdmpKckFhN1FEa3A4bzdtTUtWcnhmamNERlFuQlNTcDNNcE5LM25wVnRkNENaK1JUUExHYjh3ZEMreVB5VWswUGFvL2RuZ1hGcGZWZmM4c1ZhZXRKWEJIZ0cwYmx0L2Uvb2pTYSs0SHJYSzVoUGZXSDUyL1JVZzdmR2Z1R2lTcG5BbEw5YUlhMmk0ZWNsR3NlOFQ2dGFENFZJT3FaeFBWN0FLTW9pdFJiWGFNNSs3dFdwcFBNQTV5eHFVT1hwT3pZdEY2Q0dpemcxQ3hIY0o1ckZBK3RPTVBzN0JwZktHTDVzM2pJS3h6OGVDZmJQSHJQc0N3WDZFOGZHNENnd3NIU2JCQldzTUJxN2Q3cVc0SWkzUlJXMWFCNFlOYmlQL2lhVTJvelIyb25TT0w5NWJWWmhONjlSSUdVbXBMK2FjN0NRNWppWVlRZGJuZjZRN1dDSkhiMnpXZ3ZQRUtDTDBnaEh3ZHVmdmVOaGEweXc5SGZESS9YSWlsSTNjZHRVRUMwTmMwK0ZMZ0VCb09QWEc3M3pKMnkiLCJtYWMiOiJlOWQ2OTIzMDQ5MGE4OGExNzc0MGViNjg0MWViOGMyOGU2ZGExYTcwZmVhYzE4MTUyMDczZWQyNDdmOWJlNTU3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVRdkxydHZKWlpmT2pTN2ZTdGZpbXc9PSIsInZhbHVlIjoiVjJzd29Uc3hrQnpwOFMrMEdPMHVCNk05Vm1hdUU5Q2wzMVBSSThPcEJkUzB1ZVp3OW1nYklBQ0diWWdSTTFoRDVlQ2krWGJNaElPelJUSUtTajI4em5xbG1idUloMTVES094M0VOeXliSzJRSzhXK2ErYitqNHg5MjYwTTVhMlBOYnVrbzVJeUlRMkpvam53YzBSaG9QbzkzSElEYk0zWGN0WS9McnRGLzJYR3pDN01VVjA1akQzMVdVMytuQ1REcU55ZFcyUXpLeEIreDV4U3VqNjRvYkRjZmxGbkdsSysvM0Rzb1M4TUs3eFB2c3lvaTc5QlYrTFg1c3M4cUhsQ1o1dVoxMkRkNGo2SFNRdEF5LzhNcGduVlhVZERUcnVxa1lvQTdyT0ltU1NTV2lKUXVnTlEzcFhuTG5Jc0loK1oySFBBSDgvTXY2UjdDTkNDOGxFdDBLa0Y4L3h0VFJ4WVlXcnZiMWg2d0xyR29pRzJ5amxIeGQ0UWhvQkVSREZCajI2N3Q0WkFpK0JWWjZOOUN1YkpBcisvKy9uK1JxOWkzS2o2M3RCRThLUVlBL0JFTk1NQXJZMWJJdXppZE9zUG5lN0VNTDhoTG9GRndDK2E0cnl1OUFzbE5QMitqNWZ3VTJYc2Y3YjVaczVKZ3k3S1d0RlhOZ3ZzaEc4a0VNWWMiLCJtYWMiOiI3ZDE5NGZjY2U0ZTg5NTYzZTljZTQ2YjNhYTFlMGU5MWE4YTUwYTgxNTZjODQ5ODQxZDQ2MDVhODFmNDAzN2Q4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947919442\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-876750237 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t6clpBhZQEC0tcIVAqApy3fwEvNxCUuY7Y8VEjFB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876750237\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 02:51:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhsclYrNTFaNXhtWXVzSDh0WEI1c2c9PSIsInZhbHVlIjoiNzFER3BVdGxBd3ZIM2t3eUdNR082SjhRNWN1SEZhZndDRDdCVmZOM2ZIemZQTHBsZlp3SjYxVThxcVJkSWhPZEF3cVhEOEVGVTJlR0x2RHhCRXFpU2dwcU02cVlGdSsrWGY4SE5mc004MHBnY2FkSE1iekRpQ3dZbjZzZHhXcGNaWXNTMkxXdnY1cWw2ZVFnczdtdnBXV0J2QmkrK1BVcVB0clNUWnFGSm9WeVJsaU56U0ZHL1FiRSsyT2VrZXZjSGhTZ1I0bVdLL3d4UjJvS0d0dStBMGFzZ2FlQmx5SkY3VXN0eEZvV3pudVp1QzZaRGhGcVgxYVIyV2x4eDBvaktQNHFXcHZiY21uN3A5S1gzMnNrRGkvQk9Jc2dsbS9GVS9GM1NoZFhrNldTQkhvUSttRDNRaU1tS2xKbWNCSXUvMURFQXBBMTZiQ1hTVDdHdDFMQlIyKzJKSmNMSkxNUHNJS2pHZTNMaUhVbWlBUFMyNXRXeC92ZTJ6ajFaZ2tkQ25JcUhOL3dva1BzN21ibmlKV2pTaWRzd3lrNmRoZCt2bktHd1cvNmJkazFjaXl0b2JkZmZmOWp4RzkxbFUyVzRPeGtNODg3UDVRenFTRFBBSkQ2aERDOXlLeGxzVnNZTTA4TEpRTndmSFFscVpSOXJCN0N6WTV6NzJLZnk3Y2giLCJtYWMiOiIyNzUwNDY4YThlNGY2OGI4NDk3ZmI3YmZkZGVlNzU4NzIxOTYyNThkNWRjNDYzZWJlZTVmNmRlZTRkNzc0OTY3IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9NQzdYckNrR3l1VmxuSkwvT1JldUE9PSIsInZhbHVlIjoibjBvbHEvT1VQT21oSE5YSFd1OHh5NGRSK0YzeG5YV0ZUN2laYXFCcUtIMXNiakJXN0txVEZnKzdVd0tMbkhKRW1zSVorTWNjc0d5em1HbVRMM3I5bkFET2t2Vy9aR2tHamNkQzdKMXFyODRlNElHMlhMamRnUmc5c0h2bkFGVTZ6WUM5dWhXVmdvMzI5K3g4cWVncGtQZnBIRmV1bnhCWjZ4SERDVzd6SUREZmFIRnpUQ2VzZEx5RThRNGp1Wk45aDRqTTR3RFA2U29vOExpbU5lbjFYNEd6NkxSemw2WG9yYzY0bklNMEx2ejhjR2VGcGxsbFBkbW5CV2ViNmIva2lyZHY2ZVhaWURZSU1YNEJFekN4M3o2Q0laQ3pSY0FvSXc5TDdtWHJwcUYvNTlzb29ZR2FPUW4rTE04UG5jWFZ0Y0trVlpOQUh2Ri9vWWwyNTE1Rm1mSS9tMmk1UFNuMG43VG5JUVpZd0lpU0MzYzlQaXFQSHhPNTNQYzc1ZkVkenk0Ry9DTTBJeGRKMWpzZlo3RFh6aTRSZk4vQkxJNU1ISzRBa2pnYURKSVJSVTRLbDM2VFdLUXRsc09QeVp2Vmp6RmZiazZGVlZzKzdNU1NibTFPaEVKc1VncVgrTjJFMzNpcnF4dFJTN2wzbUdOZVJDcVhMWkE3TGxETFNkS1QiLCJtYWMiOiIxYmQ5YjZiMjgwNDE3NjBiYjZhMWZiZDhkOTYxYzA1ZDgyN2ViZjRjNzk5NmQyM2ZjM2QyMjI3NzcxZTZiOGE2IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhsclYrNTFaNXhtWXVzSDh0WEI1c2c9PSIsInZhbHVlIjoiNzFER3BVdGxBd3ZIM2t3eUdNR082SjhRNWN1SEZhZndDRDdCVmZOM2ZIemZQTHBsZlp3SjYxVThxcVJkSWhPZEF3cVhEOEVGVTJlR0x2RHhCRXFpU2dwcU02cVlGdSsrWGY4SE5mc004MHBnY2FkSE1iekRpQ3dZbjZzZHhXcGNaWXNTMkxXdnY1cWw2ZVFnczdtdnBXV0J2QmkrK1BVcVB0clNUWnFGSm9WeVJsaU56U0ZHL1FiRSsyT2VrZXZjSGhTZ1I0bVdLL3d4UjJvS0d0dStBMGFzZ2FlQmx5SkY3VXN0eEZvV3pudVp1QzZaRGhGcVgxYVIyV2x4eDBvaktQNHFXcHZiY21uN3A5S1gzMnNrRGkvQk9Jc2dsbS9GVS9GM1NoZFhrNldTQkhvUSttRDNRaU1tS2xKbWNCSXUvMURFQXBBMTZiQ1hTVDdHdDFMQlIyKzJKSmNMSkxNUHNJS2pHZTNMaUhVbWlBUFMyNXRXeC92ZTJ6ajFaZ2tkQ25JcUhOL3dva1BzN21ibmlKV2pTaWRzd3lrNmRoZCt2bktHd1cvNmJkazFjaXl0b2JkZmZmOWp4RzkxbFUyVzRPeGtNODg3UDVRenFTRFBBSkQ2aERDOXlLeGxzVnNZTTA4TEpRTndmSFFscVpSOXJCN0N6WTV6NzJLZnk3Y2giLCJtYWMiOiIyNzUwNDY4YThlNGY2OGI4NDk3ZmI3YmZkZGVlNzU4NzIxOTYyNThkNWRjNDYzZWJlZTVmNmRlZTRkNzc0OTY3IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9NQzdYckNrR3l1VmxuSkwvT1JldUE9PSIsInZhbHVlIjoibjBvbHEvT1VQT21oSE5YSFd1OHh5NGRSK0YzeG5YV0ZUN2laYXFCcUtIMXNiakJXN0txVEZnKzdVd0tMbkhKRW1zSVorTWNjc0d5em1HbVRMM3I5bkFET2t2Vy9aR2tHamNkQzdKMXFyODRlNElHMlhMamRnUmc5c0h2bkFGVTZ6WUM5dWhXVmdvMzI5K3g4cWVncGtQZnBIRmV1bnhCWjZ4SERDVzd6SUREZmFIRnpUQ2VzZEx5RThRNGp1Wk45aDRqTTR3RFA2U29vOExpbU5lbjFYNEd6NkxSemw2WG9yYzY0bklNMEx2ejhjR2VGcGxsbFBkbW5CV2ViNmIva2lyZHY2ZVhaWURZSU1YNEJFekN4M3o2Q0laQ3pSY0FvSXc5TDdtWHJwcUYvNTlzb29ZR2FPUW4rTE04UG5jWFZ0Y0trVlpOQUh2Ri9vWWwyNTE1Rm1mSS9tMmk1UFNuMG43VG5JUVpZd0lpU0MzYzlQaXFQSHhPNTNQYzc1ZkVkenk0Ry9DTTBJeGRKMWpzZlo3RFh6aTRSZk4vQkxJNU1ISzRBa2pnYURKSVJSVTRLbDM2VFdLUXRsc09QeVp2Vmp6RmZiazZGVlZzKzdNU1NibTFPaEVKc1VncVgrTjJFMzNpcnF4dFJTN2wzbUdOZVJDcVhMWkE3TGxETFNkS1QiLCJtYWMiOiIxYmQ5YjZiMjgwNDE3NjBiYjZhMWZiZDhkOTYxYzA1ZDgyN2ViZjRjNzk5NmQyM2ZjM2QyMjI3NzcxZTZiOGE2IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1097201842 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097201842\", {\"maxDepth\":0})</script>\n"}}