{"__meta": {"id": "X50fd0708fc52a0f6142b892e8d786a1b", "datetime": "2025-06-14 03:08:39", "utime": **********.677269, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749870518.201536, "end": **********.677302, "duration": 1.4757659435272217, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": 1749870518.201536, "relative_start": 0, "end": **********.487395, "relative_end": **********.487395, "duration": 1.2858591079711914, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.487413, "relative_start": 1.285876989364624, "end": **********.677305, "relative_end": 3.0994415283203125e-06, "duration": 0.18989205360412598, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45144904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00855, "accumulated_duration_str": "8.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.592033, "duration": 0.00654, "duration_str": "6.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.491}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.629709, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.491, "width_percent": 11.462}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.651481, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.953, "width_percent": 12.047}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-755050832 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749870508641%7C4%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ino0TXNleGhkYkxZRXc4S0o3VWZTaWc9PSIsInZhbHVlIjoiVGdSWHpjSHJYbVlTOTdGSkxlbzloSVZBNUJjTU44MkxoTk01cGd5RTdsWTNpN0tjd0t5OXdmYUJlNUZzeUJzRmo0YkJ3K2hkaDdmVEI0Z0lMMThmOUNrRGJkNjd2WlVpUHhXeVQyOStUdDNoSDVBNVJ3c3hLZUx4UzV5UHB3OHNVSXc1cHdqQjVnYmZmajBXNWtXOXYveG5ZTTJ4Z0tCM3k2ZCtDemZNdXJyVEFuR1BUbXU2UFc4d2NDZktIc3dEMVBnUHRacFdUdjMyZnQrZDk0U1hTdFlUbG1xVG92U1FULzdUcVhxVlFKNTRlVmhBeDdNSnBGSUJ3L1JjUUFZUG5zQmZRNGw4T1p2NHJ6OFJackNCZ25zdGRXeFpxVThrMjliTWt0SFA5cGlhZXNFTy9SSUJRUGI0V3AzOG4rN0MxQ0xQdnJYaENFREpOZE1ySmRMZ0dmWHVVMTl0NjRxRlZTSk5KWThWbFpUdjJZYnJ4S3lQZ1VNNEFtZk5IV21BQUlua2VmRUg1K29IUkNlL1NZdEplUDVVVm8vTlRvdjdEalpDQXhUMktPamVyZDZ1R0Y4Y1pab0k1bmhhR0djZkNnSmJlMTBSYnBqKzhBajJ1NmVWckJVYWxuWHNDNVVValM1NDdRN0dqenRaV05YM2RsWE1iQnczQjB3ZXphRmsiLCJtYWMiOiI5MTEwZDZhNWZlZDBhZWEzMmNmMjI4OTQ1ZGUzNGU3N2IyMzdmODZkMWRhMjQyYWQ5ODEzNGJjMzljYWQ3NmNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNDdzZxT3Bya0ZHTUZoQWFpZEJONVE9PSIsInZhbHVlIjoiT3VJejl3OCtrY1BhdkR3VXVhanpoYlFlQW15R1NtYTR2OUl5c0RWVHFTSDJaNk1CSm5zRk5NK2QxbGkrR0c3RTY3RkltcmdLWTBKU1Jsc3FyNm11dU4xc2VvTDJXTmgwaXI2SmM4SGxPblh3VjRSaVpTUUNMRlQwdnRxSElBbUg1VkRuM3Z3d2V4Y2N4ekNIU2RaTlVVZmYvRnhkZlQxQU84WHZrczdBbzhjSDByN0tvb1RuZWFVSXBxTWRSUThmUjRjRnZGTkE5QnVGRjVRUkI0WVpaRTQ2ZERVYWJNanhsZ2tRTnU1dm5pRVM2Y2NZS01iOUcweitEYVZ1R3IwNVkzK2pTbTZxUGlGSHMvRHVnN2J6QXk1ZHYvK0U0bm1BYituZWwvV1oxVkxPTGx6OXd1Z3RqVVNGMVloKzk5KzZnWVU5RWdSZ29HT0pYVGtGS3VJNlZyOElaK2ZpUW9US0lPaDkwUkFTQ2phNWlTQmdDTzdSeGdtYXRVcVRiVEZkWTBGMzRlZ2c0eU9Bc2VsSHUzaTdvdEQyNmdNTnVmUjU0Q2M2SElRSmFSWHcrMkJ5eWxxSWxqZGVsSjBWZytJN2c4M216ME9yRzl3c3g2azdsa2Nwbk55Tm1SbXZLZE93b3VvTFZmL0FtZVE5Q3h4dnlSMmtDSzJYS0pCeDdBRDUiLCJtYWMiOiIwMjU4ZmJlMjA0NzM2MjgwODg4ODFhM2YyNDU3ODAxYjI3ZmU4Y2ZmN2U1ZDI0Zjg1YzFlZGVkNmQwYThkOTBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755050832\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ryrmc4WtBXBhputH8HGgfbcOnkPzoklQrtYTNOUV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1777478254 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 03:08:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRBcG1GOCtwNUpsZkNMMVA1ZEJISXc9PSIsInZhbHVlIjoiS1BTM1ZhOXUwcm1URGRWMXltM3VIVTZZSGlFb0JMSVJZRDJmbGNvUGxXWVVaZnY0ZDVYRE5Xak5zb2ZqekE0SUhtQmpYY0NOS2p2eUVmTC9oZmgrdHNhZTBhbmE5cm16ZCt6Tk4yVmVvdXRxUEFZeldSd1doekZaaldrdkFQNUlpc28yNStCTU10dXF3dTJWeUJ0NEJqYjdabGl2SGZVZXZ0MHFJRi90M2hwR0UrQ1NCMHdoa3BZeGtGQW5HSEVoYzIwNjBMRWI1UFVLNTVQQXhTR1NXVGlqSEp4dk82WkVQN0NMU29UK2lZdE9aZ2dkVGtkc0pWa2ZHaUlBOFQwWG5GUmMrRWViaDR5QTlreDh4Z2s4a3BrL2hBUThNM1l2YXRnWlgxRS9hT2QyTC9Ub3FwQ01VSHZsdnBQdHZlbERpM01sZjA2RjV5T0ZpZEozWU9jeUhsL2xJSmEram9uNVBodDlpUkcwN1J1MUtRQ1luWTlPeSs5N2ZROXBkY3psbzhTSE40UVFWV2xGOGtsMHNtY21BdEpjY3ZzcGFZRDNvVXhJeC9BSzkwUEVYNnl4VStwNEt5M3B2Mm5od3hYQy9idEFaNXROdTJOVWtEZmZhV0pyR3MvVTc4LzN6ZXVOMnRiL01Ca0dOQ0NzNG44K1hRdTZzamJHRXFDdXBGaSsiLCJtYWMiOiI3OTNhNzAxYWIzZGJhY2FkZGQwMWE5N2ZiNGJhNDFiM2ExM2RmMmEzY2I4NzFlYjg4MGY2MmFhMmU5ZTcxY2E2IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 05:08:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpDSGo4MDY4L200TFZFVDU3bEcySnc9PSIsInZhbHVlIjoieUpBUVl2TTJ1T0VUWGxXeGIzdXE4dkdQY2FHNUt6dmdyditVOXpNVm1PcWpKdGdsZUNEOXNiNFNlQzRYSTgxVWZUbVp2ZUswVmNSYldGMDlRNCt5aHpBZ3FvZU84VmhDejY0b0R2d2JkV25IeUp5QnIxb2plVDJtWTY3OVUrR0FsRlZvNU80YndpNlBISVJRNU5BeGlzUHF3aTFnbzFiRHhLMVRPRXlrZGRPRHNoYjhsOFhLVUNhajB3MHd1SDNJRjZ6RUt5TU9xSWFzMWNpcUZQSHNtZ21Vd0lVVy9kcWd1QkNaRG15eDdtd0lhUHFGSzZkRTlEM2RDSDBVVlNQdVRBRjgzSDAzdDE3Z0ZBNlQxd0lIMXNES0lyKyt6NXdzQmVxdlNJdGN0dE8wcytubmxHamQzK2gwSVlDdDZTWlpsU21TREM4Ukt1THlweUNJQ2hrSTByV2M0elRvVW9TYlowTlVWVll1WFZ5TXJCRWxaeE9TNWh1Q2MwcUp4VDJ3ck9hNWFaWkMxa2svUFlFOXBEVTB4Z0NxaWFNQVdlZlhrRFMzV3I1Y0J2Q1E3K1l0bkZEN2p5UzdLREE0OEw3U09oWXQ3U0hwS3hLa09Vd3lBbmZRZjBRYy9oZFlKYXN5WTl0MXl4LzY5VHZQTXRjZVpJQXNlOCtOY0xkeTF5NUMiLCJtYWMiOiI4MTFlOWI0MWEzMWNkY2I1MzY1NGZjOGE0NzUyNWI0NjZlZjEzMDMwNGRiN2RlYjljNWZhOTkwNmUxZDYwNjUzIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 05:08:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRBcG1GOCtwNUpsZkNMMVA1ZEJISXc9PSIsInZhbHVlIjoiS1BTM1ZhOXUwcm1URGRWMXltM3VIVTZZSGlFb0JMSVJZRDJmbGNvUGxXWVVaZnY0ZDVYRE5Xak5zb2ZqekE0SUhtQmpYY0NOS2p2eUVmTC9oZmgrdHNhZTBhbmE5cm16ZCt6Tk4yVmVvdXRxUEFZeldSd1doekZaaldrdkFQNUlpc28yNStCTU10dXF3dTJWeUJ0NEJqYjdabGl2SGZVZXZ0MHFJRi90M2hwR0UrQ1NCMHdoa3BZeGtGQW5HSEVoYzIwNjBMRWI1UFVLNTVQQXhTR1NXVGlqSEp4dk82WkVQN0NMU29UK2lZdE9aZ2dkVGtkc0pWa2ZHaUlBOFQwWG5GUmMrRWViaDR5QTlreDh4Z2s4a3BrL2hBUThNM1l2YXRnWlgxRS9hT2QyTC9Ub3FwQ01VSHZsdnBQdHZlbERpM01sZjA2RjV5T0ZpZEozWU9jeUhsL2xJSmEram9uNVBodDlpUkcwN1J1MUtRQ1luWTlPeSs5N2ZROXBkY3psbzhTSE40UVFWV2xGOGtsMHNtY21BdEpjY3ZzcGFZRDNvVXhJeC9BSzkwUEVYNnl4VStwNEt5M3B2Mm5od3hYQy9idEFaNXROdTJOVWtEZmZhV0pyR3MvVTc4LzN6ZXVOMnRiL01Ca0dOQ0NzNG44K1hRdTZzamJHRXFDdXBGaSsiLCJtYWMiOiI3OTNhNzAxYWIzZGJhY2FkZGQwMWE5N2ZiNGJhNDFiM2ExM2RmMmEzY2I4NzFlYjg4MGY2MmFhMmU5ZTcxY2E2IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 05:08:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpDSGo4MDY4L200TFZFVDU3bEcySnc9PSIsInZhbHVlIjoieUpBUVl2TTJ1T0VUWGxXeGIzdXE4dkdQY2FHNUt6dmdyditVOXpNVm1PcWpKdGdsZUNEOXNiNFNlQzRYSTgxVWZUbVp2ZUswVmNSYldGMDlRNCt5aHpBZ3FvZU84VmhDejY0b0R2d2JkV25IeUp5QnIxb2plVDJtWTY3OVUrR0FsRlZvNU80YndpNlBISVJRNU5BeGlzUHF3aTFnbzFiRHhLMVRPRXlrZGRPRHNoYjhsOFhLVUNhajB3MHd1SDNJRjZ6RUt5TU9xSWFzMWNpcUZQSHNtZ21Vd0lVVy9kcWd1QkNaRG15eDdtd0lhUHFGSzZkRTlEM2RDSDBVVlNQdVRBRjgzSDAzdDE3Z0ZBNlQxd0lIMXNES0lyKyt6NXdzQmVxdlNJdGN0dE8wcytubmxHamQzK2gwSVlDdDZTWlpsU21TREM4Ukt1THlweUNJQ2hrSTByV2M0elRvVW9TYlowTlVWVll1WFZ5TXJCRWxaeE9TNWh1Q2MwcUp4VDJ3ck9hNWFaWkMxa2svUFlFOXBEVTB4Z0NxaWFNQVdlZlhrRFMzV3I1Y0J2Q1E3K1l0bkZEN2p5UzdLREE0OEw3U09oWXQ3U0hwS3hLa09Vd3lBbmZRZjBRYy9oZFlKYXN5WTl0MXl4LzY5VHZQTXRjZVpJQXNlOCtOY0xkeTF5NUMiLCJtYWMiOiI4MTFlOWI0MWEzMWNkY2I1MzY1NGZjOGE0NzUyNWI0NjZlZjEzMDMwNGRiN2RlYjljNWZhOTkwNmUxZDYwNjUzIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 05:08:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1777478254\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-21******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21********\", {\"maxDepth\":0})</script>\n"}}