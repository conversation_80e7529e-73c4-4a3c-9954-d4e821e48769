#!/bin/bash

# =============================================================================
# سكريبت نشر إصلاح المستخدم المنشئ
# =============================================================================

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# إعدادات الخادم (يجب تعديلها حسب بيئتك)
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"

# دالة لطباعة الحالة
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# دالة لطباعة النجاح
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# دالة لطباعة الخطأ
print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# دالة لطباعة التحذير
print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# دالة للتحقق من نجاح العملية
check_success() {
    if [ $? -eq 0 ]; then
        print_success "$1 تم بنجاح"
    else
        print_error "فشل في $1"
        exit 1
    fi
}

# بداية السكريبت
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}    نشر إصلاح المستخدم المنشئ     ${NC}"
echo -e "${BLUE}================================${NC}"
echo ""

# التحقق من وجود الملف المطلوب
print_status "التحقق من وجود الملف المطلوب..."

if [ ! -f "app/Http/Controllers/ReceiptOrderController.php" ]; then
    print_error "الملف غير موجود: app/Http/Controllers/ReceiptOrderController.php"
    exit 1
fi

print_success "الملف المطلوب موجود"

# التحقق من التغييرات المطلوبة
print_status "التحقق من التغييرات المطلوبة..."

# البحث عن التغييرات الجديدة
if grep -q "created_by = \$user->id;" app/Http/Controllers/ReceiptOrderController.php; then
    print_success "التغييرات المطلوبة موجودة في الملف"
else
    print_error "التغييرات المطلوبة غير موجودة في الملف"
    print_warning "تأكد من تطبيق التغييرات قبل النشر"
    exit 1
fi

# المرحلة 1: إنشاء نسخة احتياطية
print_status "🔄 المرحلة 1: إنشاء نسخة احتياطية..."
ssh $SERVER_USER@$SERVER_HOST "cp $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php.backup.$(date +%Y%m%d_%H%M%S)"
check_success "إنشاء نسخة احتياطية"

# المرحلة 2: رفع الملف المحدث
print_status "🚀 المرحلة 2: رفع الملف المحدث..."
scp app/Http/Controllers/ReceiptOrderController.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Http/Controllers/
check_success "رفع الملف المحدث"

# المرحلة 3: ضبط الصلاحيات
print_status "🔐 المرحلة 3: ضبط صلاحيات الملف..."
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php"
check_success "ضبط صلاحيات الملف"

# المرحلة 4: مسح الكاش
print_status "🧹 المرحلة 4: مسح الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan cache:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan view:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:clear"
check_success "مسح الكاش"

# المرحلة 5: إعادة تحميل الكاش (اختياري)
print_status "⚡ المرحلة 5: إعادة تحميل الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:cache"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan config:cache"
check_success "إعادة تحميل الكاش"

# انتهاء النشر
echo ""
echo -e "${GREEN}================================${NC}"
echo -e "${GREEN}       تم النشر بنجاح! 🎉        ${NC}"
echo -e "${GREEN}================================${NC}"
echo ""

print_success "تم نشر إصلاح المستخدم المنشئ بنجاح"
print_warning "يرجى اختبار النظام للتأكد من عمل الإصلاح"

echo ""
echo -e "${BLUE}📋 الإصلاحات المطبقة:${NC}"
echo "✅ إصلاح حفظ المستخدم المنشئ في أوامر الاستلام"
echo "✅ إصلاح حفظ المستخدم المنشئ في أوامر النقل"
echo "✅ إصلاح حفظ المستخدم المنشئ في أوامر الإخراج"
echo "✅ إصلاح حفظ المستخدم المنشئ في سجلات النقل"
echo "✅ إصلاح حفظ المستخدم المنشئ في منتجات المستودع"

echo ""
echo -e "${BLUE}🧪 خطوات الاختبار:${NC}"
echo "1. تسجيل الدخول بحساب Cashier"
echo "2. إنشاء أمر استلام جديد"
echo "3. التحقق من ظهور اسم المستخدم الصحيح كمنشئ"
echo "4. اختبار أنواع الأوامر المختلفة (استلام، نقل، إخراج)"
echo "5. اختبار مع مستخدمين مختلفين"

echo ""
echo -e "${YELLOW}⚠️  ملاحظة مهمة:${NC}"
echo "الأوامر المنشأة قبل هذا الإصلاح ستظل تظهر المستخدم القديم"
echo "الأوامر الجديدة فقط ستظهر المستخدم الفعلي المنشئ"

echo ""
print_status "انتهى النشر بنجاح! ✨"
