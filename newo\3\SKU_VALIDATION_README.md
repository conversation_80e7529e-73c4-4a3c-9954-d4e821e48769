# تحديث نظام التحقق من SKU في الوقت الفعلي

## نظرة عامة
تم إضافة نظام التحقق من تكرار الرمز التعريفي (SKU) في الوقت الفعلي لصفحة إنشاء المنتجات الجديدة.

## الملفات المحدثة

### 1. ProductServiceController.php
**المسار:** `newo/app/Http/Controllers/ProductServiceController.php`

**التحديثات:**
- إضافة دالة `checkSku()` جديدة للتحقق من تكرار SKU عبر AJAX
- الدالة تتحقق من وجود SKU في قاعدة البيانات للمستخدم الحالي
- تدعم وضع التحرير (تستثني المنتج الحالي من التحقق)
- ترجع استجابة JSON تحتوي على حالة التكرار والرسالة

```php
public function checkSku(Request $request)
{
    // التحقق من الصلاحيات
    if (\Auth::user()->can('create product & service') || \Auth::user()->can('edit product & service')) {
        $sku = $request->input('sku');
        $productId = $request->input('product_id', null);
        
        // التحقق من وجود SKU في قاعدة البيانات
        $query = ProductService::where('sku', $sku)
            ->where('created_by', \Auth::user()->creatorId());
        
        if ($productId) {
            $query->where('id', '!=', $productId);
        }
        
        $exists = $query->exists();
        
        return response()->json([
            'exists' => $exists,
            'message' => $exists ? __('الرمز التعريفي موجود بالفعل') : __('الرمز التعريفي متاح')
        ]);
    }
    
    return response()->json(['error' => __('Permission denied.')], 403);
}
```

### 2. web.php (Routes)
**المسار:** `newo/routes/web.php`

**التحديثات:**
- إضافة route جديد للتحقق من SKU

```php
Route::post('productservice/check-sku', [ProductServiceController::class, 'checkSku'])
    ->name('productservice.check.sku')
    ->middleware(['auth', 'XSS']);
```

### 3. create.blade.php
**المسار:** `newo/resources/views/productservice/create.blade.php`

**التحديثات:**

#### أ. تحديث HTML:
- إضافة `id="sku-input"` لحقل SKU
- إضافة عناصر لعرض رسائل التحقق والتحميل

```html
<div class="col-md-6">
    <div class="form-group">
        {{ Form::label('sku', __('الرمز التعريفي (SKU)'),['class'=>'form-label']) }}<x-required></x-required>
        {{ Form::text('sku', '', array('class' => 'form-control','required'=>'required', 'placeholder' => __('أدخل الرمز التعريفي'), 'id' => 'sku-input')) }}
        <div id="sku-feedback" class="mt-1" style="display: none;">
            <small id="sku-message" class=""></small>
        </div>
        <div id="sku-loading" class="mt-1" style="display: none;">
            <small class="text-muted">
                <i class="fas fa-spinner fa-spin me-1"></i>جاري التحقق...
            </small>
        </div>
    </div>
</div>
```

#### ب. تحديث JavaScript:
- إضافة متغيرات للتحكم في عملية التحقق
- إضافة event listener لحقل SKU
- إضافة دالة `checkSkuAvailability()` للتحقق عبر AJAX
- تحديث validation في النموذج

```javascript
// متغيرات التحقق من SKU
let skuCheckTimeout;
let isSkuValid = false;
let lastCheckedSku = '';

// التحقق من SKU في الوقت الفعلي
$('#sku-input').on('input', function() {
    const sku = $(this).val().trim();
    
    // مسح timeout السابق
    clearTimeout(skuCheckTimeout);
    
    // إخفاء الرسائل السابقة
    $('#sku-feedback').hide();
    $('#sku-loading').hide();
    
    // إعادة تعيين حالة التحقق
    isSkuValid = false;
    $(this).removeClass('is-valid is-invalid');
    
    // إذا كان فارغ، لا تتحقق
    if (sku === '') {
        lastCheckedSku = '';
        return;
    }
    
    // إذا كان نفس آخر تحقق، لا تتحقق مرة أخرى
    if (sku === lastCheckedSku) {
        return;
    }
    
    // عرض التحميل
    $('#sku-loading').show();
    
    // تعيين timeout للتحقق (debounce)
    skuCheckTimeout = setTimeout(function() {
        checkSkuAvailability(sku);
    }, 500); // انتظار 500ms بعد توقف المستخدم عن الكتابة
});

// دالة التحقق من توفر SKU
function checkSkuAvailability(sku) {
    $.ajax({
        url: '{{ route("productservice.check.sku") }}',
        method: 'POST',
        data: {
            sku: sku,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            $('#sku-loading').hide();
            lastCheckedSku = sku;
            
            if (response.exists) {
                // SKU موجود - عرض خطأ
                $('#sku-input').removeClass('is-valid').addClass('is-invalid');
                $('#sku-message').removeClass('text-success').addClass('text-danger').text(response.message);
                $('#sku-feedback').show();
                isSkuValid = false;
            } else {
                // SKU متاح - عرض نجاح
                $('#sku-input').removeClass('is-invalid').addClass('is-valid');
                $('#sku-message').removeClass('text-danger').addClass('text-success').text(response.message);
                $('#sku-feedback').show();
                isSkuValid = true;
            }
        },
        error: function(xhr) {
            $('#sku-loading').hide();
            console.error('SKU check failed:', xhr);
            
            // عرض خطأ عام
            $('#sku-input').removeClass('is-valid').addClass('is-invalid');
            $('#sku-message').removeClass('text-success').addClass('text-danger').text('خطأ في التحقق من الرمز التعريفي');
            $('#sku-feedback').show();
            isSkuValid = false;
        }
    });
}
```

## الميزات الجديدة

### 1. التحقق في الوقت الفعلي
- يتم التحقق من SKU أثناء كتابة المستخدم
- استخدام تقنية debounce (انتظار 500ms بعد توقف الكتابة)
- عرض مؤشر تحميل أثناء التحقق

### 2. رسائل واضحة
- رسالة خضراء عند توفر SKU: "الرمز التعريفي متاح"
- رسالة حمراء عند تكرار SKU: "الرمز التعريفي موجود بالفعل"
- رسالة تحميل: "جاري التحقق..."

### 3. منع الإرسال
- لا يمكن إرسال النموذج إذا كان SKU مكرر
- التحقق من صحة SKU قبل الإرسال
- رسائل خطأ واضحة للمستخدم

### 4. تحسين الأداء
- تجنب الطلبات المتكررة للـ SKU نفسه
- استخدام debounce لتقليل عدد الطلبات
- إلغاء الطلبات السابقة عند الكتابة الجديدة

## كيفية الاختبار

### 1. اختبار يدوي
1. انتقل إلى صفحة إنشاء منتج جديد
2. اكتب رمز SKU في الحقل المخصص
3. انتظر 500ms وراقب الرسائل
4. جرب SKU موجود مسبقاً
5. جرب SKU جديد
6. حاول إرسال النموذج مع SKU مكرر

### 2. اختبار تلقائي
تم إنشاء ملف `test_sku_validation.html` لاختبار الوظيفة بشكل منفصل.

## الأمان

### 1. التحقق من الصلاحيات
- التحقق من صلاحية إنشاء أو تحرير المنتجات
- رفض الطلبات غير المصرح بها

### 2. CSRF Protection
- استخدام CSRF token في جميع الطلبات
- حماية من هجمات Cross-Site Request Forgery

### 3. تصفية البيانات
- تنظيف وتصفية بيانات SKU المدخلة
- التحقق من صحة البيانات في الخادم

## التوافق

### المتطلبات:
- Laravel Framework
- jQuery
- Bootstrap (للتنسيق)
- Font Awesome (للأيقونات)

### المتصفحات المدعومة:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## الصيانة المستقبلية

### تحسينات محتملة:
1. إضافة cache للـ SKU المتحقق منها
2. تحسين رسائل الخطأ
3. إضافة اقتراحات SKU بديلة
4. دعم التحقق المجمع (bulk validation)

### مراقبة الأداء:
- مراقبة عدد طلبات التحقق من SKU
- قياس زمن الاستجابة
- تتبع معدل نجاح/فشل التحقق
