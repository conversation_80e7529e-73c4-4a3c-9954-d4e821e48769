# نظام توصيل الطلبات في POS

## نظرة عامة

تم تطوير نظام توصيل الطلبات ليعمل مع جميع موظفي الكاشير (Cashier) في النظام. عند اختيار عميل لديه صلاحية توصيل في شاشة POS ADD، يتم تحويل الفاتورة إلى حالة "جاري توصيل الطلب" وتسجيل المبلغ كعجز في عمود Delivery Cash لموظف الكاشير المسؤول.

## الميزات الجديدة

### 1. إدارة حالات التوصيل
- **عادي (normal)**: الفواتير العادية
- **جاري توصيل الطلب (delivery_pending)**: الطلبات قيد التوصيل
- **تم توصيل الطلب (delivery_completed)**: الطلبات المكتملة

### 2. ربط الفواتير بالكاشيرز
- تحديد الكاشير المسؤول عن كل طلب توصيل
- تسجيل العجز في السجل المالي للكاشير

### 3. إدارة النقد المحسنة
- عمود **Delivery Cash** في شاشة إدارة النقد
- تسجيل تلقائي للعجز عند إنشاء طلب توصيل
- تحديث تلقائي عند تحصيل الدفع

## التغييرات التقنية

### قاعدة البيانات

#### جدول `pos`
```sql
ALTER TABLE pos ADD COLUMN delivery_status ENUM('normal', 'delivery_pending', 'delivery_completed') DEFAULT 'normal';
ALTER TABLE pos ADD COLUMN cashier_id BIGINT UNSIGNED NULL;
ALTER TABLE pos ADD FOREIGN KEY (cashier_id) REFERENCES users(id) ON DELETE SET NULL;
```

#### جدول `financial_records`
- عمود `delivery_cash` موجود بالفعل

### الملفات المحدثة

#### 1. النماذج (Models)
- **`app/Models/Pos.php`**
  - إضافة حقول `delivery_status` و `cashier_id`
  - إضافة علاقة `cashier()`
  - إضافة ثوابت `$deliveryStatuses`

#### 2. الكنترولرز (Controllers)
- **`app/Http/Controllers/PosController.php`**
  - تحديث دالة `store()` لمعالجة طلبات التوصيل
  - إضافة دالة `recordDeliveryDeficit()`
  - إضافة دالة `processDeliveryPayment()`

#### 3. العروض (Views)
- **`resources/views/pos/index.blade.php`**
  - تحديث JavaScript لمعالجة العملاء ذوي صلاحية التوصيل
  - إضافة تنبيه للكاشيرز عند اختيار عميل توصيل

- **`resources/views/pos/report.blade.php`**
  - تحديث عرض حالات الدفع لإظهار حالات التوصيل

- **`resources/views/pos/view.blade.php`**
  - إضافة زر "تحصيل الدفع" للطلبات قيد التوصيل

#### 4. المسارات (Routes)
- **`routes/web.php`**
  - إضافة مسار `pos.process.delivery.payment`

## كيفية الاستخدام

### للكاشيرز

1. **إنشاء طلب توصيل:**
   - اختر عميل لديه صلاحية توصيل
   - سيظهر تنبيه "هذا العميل لديه صلاحية توصيل"
   - أكمل الطلب كالمعتاد
   - سيتم تسجيل المبلغ كعجز في Delivery Cash

2. **تحصيل دفع طلب توصيل:**
   - اذهب إلى POS Summary
   - ابحث عن الطلبات بحالة "جاري توصيل الطلب"
   - انقر على الطلب للدخول إلى التفاصيل
   - انقر على زر "تحصيل الدفع 💰"

### لمديري النظام

1. **مراقبة طلبات التوصيل:**
   - شاشة POS Summary تعرض جميع حالات التوصيل
   - شاشة إدارة النقد تعرض عمود Delivery Cash

2. **إدارة صلاحيات العملاء:**
   - في إعدادات العميل، فعل خيار "صلاحية التوصيل"

## الاختبار

لاختبار النظام، قم بتشغيل:

```bash
php test_delivery_system.php
```

## الأمان والصلاحيات

- الكاشيرز يمكنهم إنشاء وتحصيل طلبات التوصيل
- مستخدمو التوصيل يمكنهم إنشاء طلبات التوصيل فقط
- المديرون يمكنهم الوصول لجميع الوظائف

## الدعم الفني

في حالة وجود مشاكل:

1. تحقق من صلاحيات المستخدم
2. تأكد من تفعيل صلاحية التوصيل للعميل
3. تحقق من وجود وردية مفتوحة
4. راجع سجلات النظام للأخطاء
