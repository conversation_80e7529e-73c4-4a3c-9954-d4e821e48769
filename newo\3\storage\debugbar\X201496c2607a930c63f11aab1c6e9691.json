{"__meta": {"id": "X201496c2607a930c63f11aab1c6e9691", "datetime": "2025-06-14 02:51:01", "utime": **********.465891, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.418483, "end": **********.465915, "duration": 1.****************, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": **********.418483, "relative_start": 0, "end": **********.230914, "relative_end": **********.230914, "duration": 0.****************, "duration_str": "812ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.231035, "relative_start": 0.****************, "end": **********.465917, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0262, "accumulated_duration_str": "26.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3081388, "duration": 0.01794, "duration_str": "17.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.473}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.347054, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.473, "width_percent": 5.611}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.440757, "duration": 0.00679, "duration_str": "6.79ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 74.084, "width_percent": 25.916}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749869447409%7C1%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im11S2Z3aUJqdExKcWN0OGpIa3Z3bkE9PSIsInZhbHVlIjoiRm1JQWFlaCtWN1FYcEdRNGZPRFdiZUpVaGtjYnpZUkMvaDZYanJuQmh3VnRqeDZXUHpsc0xFT2JydXJZVUYwY2RVZ091ZGQ5S0RmOUt1WHc5ai90dzUwdG13M2JnSDV6MGlMdlF0UnNkbU9wcWRzRUtPUnZ1L0o3NGVVY0Z4TnRtZVliNXRmaWh6YXpEdVdBZGd6bGpzWFdCbWVjK2FhbHpNNllVOXN3UERRVzFVNW1NcVJzNkozcmllditreTA2UVpaZk9QbXRzY0VMR1VEdk5hcmxjclRNSXQxa1NqeTRCSlk4LzE4UWk4NDdCcmg4VG1hT3J6WUtyYWtXZEhxNDIraGZjWGk5dWREc1h1YVlwdHRQemMrWXJyZDdtNkttb0pXVFVBcjJ3bll0Z3JlYzJhS3B6L01RbWZXbDlpa0ZhckpLK2JMNVQvSXlleGZwTVlvbGhZbnM3S0EzdWlBcWs3VzUrZy9BSDRZTDhlMVl0VUNHZEVXTTN1b3l2OS9heHlWSEVaODRGTVM4ZUNYSWRhOTBSWS9OcjdtUWRuQ00rZDgvV3pIT01kR3lJVVQ0UVRFR284cGowOVBDdkhqZ0krZVVOdGRhRlM4T0o1SXJBT2trMFVLK3gxMDlCWlB1T3lROXpWNElPMllFK3FIVHFNbW90NEdlaEtXNVN5a2oiLCJtYWMiOiJmOTgzZGVhZmE0MTA3OWMwYjlkM2M4ZWQ5MjMwYmVkZmVjZDQwMzM1MTQ0NWRjMTk1Njg1NWU5NjgxMGFjZTVlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktNL2lQLzRyQndPZ0grOXYrRVRxZ0E9PSIsInZhbHVlIjoiYkV6WVVZZHpkQ3c5SE84SG05Mkk0a01VNUhsclZzaG8rQVEyVTVCWno5ei93dHpjVUhMRU9idEJzc2QzNVozOTlPd3VQa0xyci9YRUg2bHF2QVk4emdyMUhuYnVoKzljSUVPTWpkcVZ2Qk9pZHA3K0dNY09lcEtaQU5URjZsaEJNc0pUaTNpbXVqUjZ3cWR0akgxNGwzTlI4YkRJalV5dDNlSjh0UkdRZHlCKzAwN21BYTIxUk4wTVpRYWhLNTZybUdFWW1NTmlZWW84MEYrVk15aFQvTDlWY05wM2R2UkZ5ajhmQVFMVEJlSzRZcmJaZHhEazBxYXFHcG8wai9CdG5WanRMMUI3b0xGSGEybkcxT0dCZzcvQXNYN3I4VVp6STFDUnowcWZESWt3MjcwSVVrYS8rOGxSMlRkNUljMTBqZkdjeEdBUkQvcVlKZytsaU9nNTVnWmRIRmh3SkZUdy9WRDR0SjlTQzlocTNnVis0TVVCTmdDZG1sMTBtamU5SExLREg1YkEvWng3Unp3WWlLOU4reUlxemxpV2RuLzZVUmNuQXYrQVNVTmJFT3l3S3R5ZFhWZ1FZS0R1YkdLUlNsVFE4VTRNSWRPYW84MzRzeG5YUHNycUxQUGcvUDVGeS8yMHFhR2s5dTViM0VuRkRjVG9ORnFJUVJmZGxNaEUiLCJtYWMiOiI1MWU0ZGQyNGFlOTBhZDIxN2QyZTBkYmIwN2M1ZDRiMjMzZjZmYTc4OGIxODYyYzM1ZDMyYmYyN2I1YTAwNDBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1349463718 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t6clpBhZQEC0tcIVAqApy3fwEvNxCUuY7Y8VEjFB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349463718\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-502215885 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 02:51:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5wU3podXFRNy9MSDBmZENROVJEbXc9PSIsInZhbHVlIjoiaXIyYTlOUXo3dHorWDJuZkhhMy93eHdTMVpqcjhHV2VNbU9FcHpSUjVPcW9mWFN5bDZLeGRUdjljOUlRUHlpTjl3WU9XSUVCRFkwVnFJY3JodG0zRlROUUVSdkFLai9Md2NXY3oxT094cVM1cXIxeFNNSlFMbmZKWU9zbnFaMHNLRU1XOUtFTnJkQ3BuSGtUanV1QXFZYjNQRU52M3NyN1RvK2o3MEFMbXN1OXczYXNaWllMR0N2dG9QYnZ1WHV0SGVaY2oyUFBTY0tKVnNrRER2ay9na0VZOVIvc0c3RXJLZWJQWk5QTWNKcTdMV0hNTG0xa1VMbWtYaHh3Y1NwdWN6UGYwazBreGVxUmVOcm5XNWhEd0pGVVhpODNUL1pwZVpQSlNLQ0F6Vk0rUHp1cXhKYVhQVmMvSk9VS1ZJS0pEVlhLazNLaWZjNG9jRDRCcjJLakRSYzd2RjFGWlNZYTEvVkthNlYvT3N3L3hzRVdTeU53L3F3WnlXU25TaUFiUmp1cHdIUG8ydXJVME5CblIrREEyK2l6RXoyNkdrT0ptTTFVSVdRbytnSERWQ0taNU9sSnlGMmsvVDJYUm8yL0RtRXRKYzdhRjBpZzNzMnJIWHdvUWJ5c2FsYlZ0WGhlYmJaQk1FUGNwUmFWcDJ0czlLTVNiaGM5Q1JrRVJaWGIiLCJtYWMiOiIxYTQ2NGU1ZmI4ZWM1MjY1MGExZWUwNTVjYzMyOGU5NDJmYWE5Y2QxNmMwNjFkMmE1NWFlMTg1M2IxYTI2YTA5IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFGd3JaL3ptb1hWSEo2TVMwVFJaWWc9PSIsInZhbHVlIjoiRjhReTdyTlR3eVpwaENNNWtuYUVYZm04bUZEYVV0bFNFKzJiVi8vNkx2N0hLRk9vNStIbTMvMXlsaTZuM05rS1U0UGlHNTNycy92d3hsMkx5MmN4QmRNZ010QkFyVFRmSG14b3NRY0d0ejJ5NjBuL0hvY2YrLy9PS295RS9kY3VXbVNaNDNIVE8yZDNJZHd1cysyS2wxVXYrTXVsNTZNNGNMc09kUE5IV2VTYTdrMzZ5Y2kxKzNpa2Z5SmZYOURmZlg5a0lwN2h1NlRVUHVSeGwvM21KRURmNDlqWWZuTGxRcVUwQXhoWmtQVlVBYTIzZk1SVlcvWkYwVjZSaTNGL01vS0swcWo5ejEvcktVUHptTk1vYlkzb29XMTRUK3hSMXliU3lOZHpwUW1XODJZaEtDU3hRa1NtUk1pZ1lQTjlYVTcyU3VnUlFPTnVORmtiaXprUDB4OXFpcm5BQ0p2S3hObjRRL0ZleWZUZXpFblZ4SjBGM2NtcE1YeGhFTkpyZGx6WFpCdmtKb2VXNXdDYmJwYmpGQ2VlTTEvMlV2OWVGa0Iza0dNOEJETTBaWG4wTy9YbnVUd3VEWSs3Vlp2TG1DeXIySXFMS0lEbysxWlVOd004ZGE5MHIvam9ubWxJWlg5UytKc0Nicm9IMlVUdVlTV0taRC9DVmhsVnk0amoiLCJtYWMiOiI0MjBiYWEyNzIwYzljOGY5M2JlOTg0NWY0OGNhYWM1MmNmZTM3MDQ4MmZkNjI5NjI5YzQ1ZWFlMzJlMjQzMzE4IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5wU3podXFRNy9MSDBmZENROVJEbXc9PSIsInZhbHVlIjoiaXIyYTlOUXo3dHorWDJuZkhhMy93eHdTMVpqcjhHV2VNbU9FcHpSUjVPcW9mWFN5bDZLeGRUdjljOUlRUHlpTjl3WU9XSUVCRFkwVnFJY3JodG0zRlROUUVSdkFLai9Md2NXY3oxT094cVM1cXIxeFNNSlFMbmZKWU9zbnFaMHNLRU1XOUtFTnJkQ3BuSGtUanV1QXFZYjNQRU52M3NyN1RvK2o3MEFMbXN1OXczYXNaWllMR0N2dG9QYnZ1WHV0SGVaY2oyUFBTY0tKVnNrRER2ay9na0VZOVIvc0c3RXJLZWJQWk5QTWNKcTdMV0hNTG0xa1VMbWtYaHh3Y1NwdWN6UGYwazBreGVxUmVOcm5XNWhEd0pGVVhpODNUL1pwZVpQSlNLQ0F6Vk0rUHp1cXhKYVhQVmMvSk9VS1ZJS0pEVlhLazNLaWZjNG9jRDRCcjJLakRSYzd2RjFGWlNZYTEvVkthNlYvT3N3L3hzRVdTeU53L3F3WnlXU25TaUFiUmp1cHdIUG8ydXJVME5CblIrREEyK2l6RXoyNkdrT0ptTTFVSVdRbytnSERWQ0taNU9sSnlGMmsvVDJYUm8yL0RtRXRKYzdhRjBpZzNzMnJIWHdvUWJ5c2FsYlZ0WGhlYmJaQk1FUGNwUmFWcDJ0czlLTVNiaGM5Q1JrRVJaWGIiLCJtYWMiOiIxYTQ2NGU1ZmI4ZWM1MjY1MGExZWUwNTVjYzMyOGU5NDJmYWE5Y2QxNmMwNjFkMmE1NWFlMTg1M2IxYTI2YTA5IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFGd3JaL3ptb1hWSEo2TVMwVFJaWWc9PSIsInZhbHVlIjoiRjhReTdyTlR3eVpwaENNNWtuYUVYZm04bUZEYVV0bFNFKzJiVi8vNkx2N0hLRk9vNStIbTMvMXlsaTZuM05rS1U0UGlHNTNycy92d3hsMkx5MmN4QmRNZ010QkFyVFRmSG14b3NRY0d0ejJ5NjBuL0hvY2YrLy9PS295RS9kY3VXbVNaNDNIVE8yZDNJZHd1cysyS2wxVXYrTXVsNTZNNGNMc09kUE5IV2VTYTdrMzZ5Y2kxKzNpa2Z5SmZYOURmZlg5a0lwN2h1NlRVUHVSeGwvM21KRURmNDlqWWZuTGxRcVUwQXhoWmtQVlVBYTIzZk1SVlcvWkYwVjZSaTNGL01vS0swcWo5ejEvcktVUHptTk1vYlkzb29XMTRUK3hSMXliU3lOZHpwUW1XODJZaEtDU3hRa1NtUk1pZ1lQTjlYVTcyU3VnUlFPTnVORmtiaXprUDB4OXFpcm5BQ0p2S3hObjRRL0ZleWZUZXpFblZ4SjBGM2NtcE1YeGhFTkpyZGx6WFpCdmtKb2VXNXdDYmJwYmpGQ2VlTTEvMlV2OWVGa0Iza0dNOEJETTBaWG4wTy9YbnVUd3VEWSs3Vlp2TG1DeXIySXFMS0lEbysxWlVOd004ZGE5MHIvam9ubWxJWlg5UytKc0Nicm9IMlVUdVlTV0taRC9DVmhsVnk0amoiLCJtYWMiOiI0MjBiYWEyNzIwYzljOGY5M2JlOTg0NWY0OGNhYWM1MmNmZTM3MDQ4MmZkNjI5NjI5YzQ1ZWFlMzJlMjQzMzE4IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502215885\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-8******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8********\", {\"maxDepth\":0})</script>\n"}}