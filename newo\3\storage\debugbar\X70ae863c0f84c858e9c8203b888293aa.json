{"__meta": {"id": "X70ae863c0f84c858e9c8203b888293aa", "datetime": "2025-06-14 02:51:05", "utime": **********.600328, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749869464.152249, "end": **********.600368, "duration": 1.4481189250946045, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1749869464.152249, "relative_start": 0, "end": **********.350157, "relative_end": **********.350157, "duration": 1.1979079246520996, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.350184, "relative_start": 1.197934865951538, "end": **********.600374, "relative_end": 5.9604644775390625e-06, "duration": 0.25019001960754395, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45205688, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02268, "accumulated_duration_str": "22.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.437983, "duration": 0.01757, "duration_str": "17.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.469}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4827359, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.469, "width_percent": 7.981}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.551831, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 85.45, "width_percent": 7.804}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.57377, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.254, "width_percent": 6.746}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1191392747 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1191392747\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-624351109 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-624351109\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2106235536 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106235536\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-429785453 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749869447409%7C1%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkY2MjI4ei9idHYvTElXWUcrZEFjd1E9PSIsInZhbHVlIjoiM3JuRjgyellPSDlUdFdJV2tDM0VMNm52aFY2OWh1UmsvWkthTGJlOWZmMmR6ODNkUWlVazNiek1seWswanE5R0x6bW1oRW5vek0zZkY5OE1FNERucGZ4dFFkS0lldTRnQXh1WnhMZitUck9oZHNlZ2g3cm4vSVYvY0Q0MFFVRml6ZTFIRFRHYVg1dWtYV2pqVjVEUUlOdVZqREcyaTRHQ214eUNRa1lSOUxTc0hNY09zMjZteWNyN1paWElFMERWbkduWE1LSlJwMjR1V3BYVHpkR0FTbVgzaFpWYUZxTDh4K2xBYWlETzRZVjAxVGp2Y2hna2hoYW92TjVqdnQ4SnFVUmZDbkxVbk1md3N0dkVwZXFtekhZcU9GSDl6U0kwUHRwVVQva0RJUHJTVUVXWXhUQU5uUkNoT0tkc1hqbTlscSsrczRnZnVMV3VyaVNlaExzbTZHSFE1RUw1b2tobUtqUkV6U0RWS3lVZ3FHR2tKYVNpSkJDQ1MwWHFoQ2srLzFqR0V0S0g2UE9QSzFIRlVoT1dWZzdDWXlSTXFZdGlFb1ZXOWgyd1dVVS9SeXZRNXQ2Z0JFWXMrVXFpM0pVYnB3T0hiYW1zOUNHTG9QdnhFMFgrMFBqTFVYV1ZYcm5kanBFTS9VN0xlRnEwMTRzVFJHM0phWnFYNGQ3bmVvaVYiLCJtYWMiOiIwNjJiYWI2ODAyNzZmMmFjMGU3NWIyZDk2YTY2ZDcyN2IyNzc5NDI5MzZmODFmZmU1YTZhNDI5MWI5M2U4MTI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imt1MkFpcGJvcTFoaGRSaWdncUR3L0E9PSIsInZhbHVlIjoiMnNGWmZzS3ZxQTFrN2ZZOFB3dUhWcFVlN2NDOGU5SzdTVStBVXViQmFoUldvZXRFM2k3aEV6WW9ZYXoyOXZtcGtPc3BBQmJjQzdwTmFHTnQwY0kyTjEvL0VSamxxdk45QUowa0IxQ0tkbHRpcjhvKytDQ2VtVjdZN0RZSTliUEhlLzVjaGYycWZHMDBKeGFkRzRQdlNqdnAxbFlZeUxQcEZ6ek1JdFVuQlg2TUtSa2ZFcGllaTdRcmttd2w4YjF5UG5rNXUwRHYrTHhVWkhlajZMak13a1V1SExta1VuT2hIclFoVHFXN1FpRHBiaVBTdEpNb1puUVRHbUQ1WXhFVHNNRXFqdWlSczV4dWVGRkJlM1JKblhPV05BWkR2QkdNYXVXUkJGUDZUWkk4enprUFdsR2xZZ3VSNWNQbnlwMTdmMWwyOHpGNW5iNlduSXBqZW94VC9RRzRWQmhub2ZEeDRCSHFXUTU3anJzSUtabzNBeGhVQ2V0SldqdVhSdzJmS240SHFadzVMN05Xa2NDNHFaakh3YjNNck5zTDJEUHRBVzNEY1JqY3ZRVDByemdFanZoelRYanQ1SGVoWEErVy9sU2pXMzQrUmE2Qm9uWThtVm9OVDRHWWJWNXVUYjlpRlQzLzQyK2NWWnRnQkt4MThMYlhlaE0wNUwzK29NWFIiLCJtYWMiOiJmNjg2ODNlNmRhMDM2ZmI3MDcwYjI5NmJmNWJhZjhmODhhNDg2NWNlZmVjYzMzMDE2MjJhMjVlYTQyYTIzYTQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429785453\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1492167834 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t6clpBhZQEC0tcIVAqApy3fwEvNxCUuY7Y8VEjFB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492167834\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-761851493 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 02:51:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZwK003Y3FtdmRQWnpUTGJmWFN2S2c9PSIsInZhbHVlIjoibkdYdnhhUEtScStSL2ExZEprY3diU1JBN2ZSSmhaTThodWZSWSt2Q1ZvRUNzdXJ2S1dsY3dZQ2JPbUpKNWJhQXRiTVpsTCtURnhiQWFJdVBJdEdBcUlIeGUzUjFoa2FYOXdwVDFUdXRYbU1ubUlJRmhxN1NMRlY3dzdEQlBlbHJ5aS8xSWFOUkFZZXc3cHdEalp5N0xMWWt3NjkyMlNrL0ZQNkRjdWwvVnl0cEFDSm5UTWdmVkJ4VmpzMnIzbmdyTGR1Y2Z0dUNsV25rbjkxMWNPQ2EvTmgwanRFTkVEU1BtSFFKKzNYdkVJb3p3bUVMMy9RaGFpK05WYnhxc2w0VkNoKytMdmpPbTVFVVAxUzlVQWRqSzhxc2kwemxJb0JXV2RrMjZxNnpEUFV2MVA4WXVrZUFjNEJOWGJRUmU1QkZ6NmtGYVBHNzBkKzlnRzQ3UkJLZG9KdFhpQU8wNjFzZGJyNjhPUFQrWWd6SXVQVnNZUXBReFY1OUF2amQ5SFRreXozczFDV0w1VzY2RC9kTGc4YUEwZzlqTWh5S2ltb0RBYTI5Sm9RSWVid2I3QkFpbFhDemgzTGZXVmFDTGxSODBOK2xKb0pOMENWRUlGTG5CbjFLVSs2ZXNFU01qVWVnaEVCOUF4OWM0bXNpRFpTbFAwMkZlY1hnbXh5VTkwbnoiLCJtYWMiOiIzMzdkYTQ0ZGNlZDNlNTk0Mzc4YjhkMDExMWI1MmRhNTI4YmY4MmMyMDlmZjNmZTk5MzZjN2JkZDAyYmY3MjczIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdLZ3ZHSU00UUxrS241dEdaK1ZxbXc9PSIsInZhbHVlIjoiMlNwVVFnTitwMC9zc1ZXakJlU1NlSnB6Q2tYbWNhWEY4YTFyTjFHTGF1dVFhYTYzZFFQQzRiM24rN0EyZURsRUFYMU5qMjRuRWtDMzFwT2E5Y3d6NXFlMVVRSHZJNFFNVVlJQlM1N1pyTUZybHlnTXYzY29DMm5hVEh6a0Y4N2lzSWhqSXlNVVdMWUFyRm1IbzJvcmRtbU5NY2N4dW1FYlhwRm5BdGtvNzFVRG1VMEhQT2xFRWpXd2tyWUl6NG0weW9ldVpDME5xYWw1eXl1aWU1WUlyMkNWa29yQmxqd1lXbXpTNTNHQ2VwempGR1ovRUhqY3FxdTEvdVQrTlhMaG5xRnJvUnFJZUM1VFpGUE5UQ1ZxMDhqdi9YV2Y4RTI5SHpMcTFPVGJSd1g2RXVIbkhwaFhoK3lCbUROMzFEdzZhSUdBWHBENVJNRzhZaHNjVFo2b2pYUjh6dXZaaXh4bE96clg1UDZLZzhYeFhrdnlYS2t3QjFDWkhadjFCVXhRN0d0eWFwYW0rdkd3VVdheDhnSTA2OUZXWW1lTktVTjNwNnNBU2pLYU9VMG1CTk04ek8xNjIxbzI2c1Q1ekQ1ZDZ2TUlZWGxMWHpjRnlFMGpCWEtaeGRpOXRrYTRQeS90L3JPTUExMitmemxIU2piUTBJd2JqMDJ0Z2RGc2YwRVUiLCJtYWMiOiI5MDI3MWUyOWNkZDRjZmNjOTI1NTViY2IzNWI2ZjlmNWZlOWRlZDY1YmY1YThkYjJhYWVhZGJjM2M3ZGNmOTM4IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZwK003Y3FtdmRQWnpUTGJmWFN2S2c9PSIsInZhbHVlIjoibkdYdnhhUEtScStSL2ExZEprY3diU1JBN2ZSSmhaTThodWZSWSt2Q1ZvRUNzdXJ2S1dsY3dZQ2JPbUpKNWJhQXRiTVpsTCtURnhiQWFJdVBJdEdBcUlIeGUzUjFoa2FYOXdwVDFUdXRYbU1ubUlJRmhxN1NMRlY3dzdEQlBlbHJ5aS8xSWFOUkFZZXc3cHdEalp5N0xMWWt3NjkyMlNrL0ZQNkRjdWwvVnl0cEFDSm5UTWdmVkJ4VmpzMnIzbmdyTGR1Y2Z0dUNsV25rbjkxMWNPQ2EvTmgwanRFTkVEU1BtSFFKKzNYdkVJb3p3bUVMMy9RaGFpK05WYnhxc2w0VkNoKytMdmpPbTVFVVAxUzlVQWRqSzhxc2kwemxJb0JXV2RrMjZxNnpEUFV2MVA4WXVrZUFjNEJOWGJRUmU1QkZ6NmtGYVBHNzBkKzlnRzQ3UkJLZG9KdFhpQU8wNjFzZGJyNjhPUFQrWWd6SXVQVnNZUXBReFY1OUF2amQ5SFRreXozczFDV0w1VzY2RC9kTGc4YUEwZzlqTWh5S2ltb0RBYTI5Sm9RSWVid2I3QkFpbFhDemgzTGZXVmFDTGxSODBOK2xKb0pOMENWRUlGTG5CbjFLVSs2ZXNFU01qVWVnaEVCOUF4OWM0bXNpRFpTbFAwMkZlY1hnbXh5VTkwbnoiLCJtYWMiOiIzMzdkYTQ0ZGNlZDNlNTk0Mzc4YjhkMDExMWI1MmRhNTI4YmY4MmMyMDlmZjNmZTk5MzZjN2JkZDAyYmY3MjczIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdLZ3ZHSU00UUxrS241dEdaK1ZxbXc9PSIsInZhbHVlIjoiMlNwVVFnTitwMC9zc1ZXakJlU1NlSnB6Q2tYbWNhWEY4YTFyTjFHTGF1dVFhYTYzZFFQQzRiM24rN0EyZURsRUFYMU5qMjRuRWtDMzFwT2E5Y3d6NXFlMVVRSHZJNFFNVVlJQlM1N1pyTUZybHlnTXYzY29DMm5hVEh6a0Y4N2lzSWhqSXlNVVdMWUFyRm1IbzJvcmRtbU5NY2N4dW1FYlhwRm5BdGtvNzFVRG1VMEhQT2xFRWpXd2tyWUl6NG0weW9ldVpDME5xYWw1eXl1aWU1WUlyMkNWa29yQmxqd1lXbXpTNTNHQ2VwempGR1ovRUhqY3FxdTEvdVQrTlhMaG5xRnJvUnFJZUM1VFpGUE5UQ1ZxMDhqdi9YV2Y4RTI5SHpMcTFPVGJSd1g2RXVIbkhwaFhoK3lCbUROMzFEdzZhSUdBWHBENVJNRzhZaHNjVFo2b2pYUjh6dXZaaXh4bE96clg1UDZLZzhYeFhrdnlYS2t3QjFDWkhadjFCVXhRN0d0eWFwYW0rdkd3VVdheDhnSTA2OUZXWW1lTktVTjNwNnNBU2pLYU9VMG1CTk04ek8xNjIxbzI2c1Q1ekQ1ZDZ2TUlZWGxMWHpjRnlFMGpCWEtaeGRpOXRrYTRQeS90L3JPTUExMitmemxIU2piUTBJd2JqMDJ0Z2RGc2YwRVUiLCJtYWMiOiI5MDI3MWUyOWNkZDRjZmNjOTI1NTViY2IzNWI2ZjlmNWZlOWRlZDY1YmY1YThkYjJhYWVhZGJjM2M3ZGNmOTM4IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761851493\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2132634095 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132634095\", {\"maxDepth\":0})</script>\n"}}