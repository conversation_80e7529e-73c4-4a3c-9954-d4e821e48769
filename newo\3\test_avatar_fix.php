<?php
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Get current user
$user = \Auth::user();
if (!$user) {
    echo "لا يوجد مستخدم مسجل دخول. يرجى تسجيل الدخول أولاً.";
    exit;
}

echo "<h2>تشخيص مشكلة الصورة الشخصية</h2>";
echo "<p><strong>اسم المستخدم:</strong> " . $user->name . "</p>";
echo "<p><strong>حقل الصورة في قاعدة البيانات:</strong> " . ($user->avatar ?: 'فارغ') . "</p>";

// Check storage settings
$settings = \App\Models\Utility::getStorageSetting();
echo "<p><strong>نوع التخزين:</strong> " . $settings['storage_setting'] . "</p>";

// Check get_file function result
$profile = \App\Models\Utility::get_file('uploads/avatar/');
echo "<p><strong>نتيجة دالة get_file:</strong> " . $profile . "</p>";

// Check if avatar file exists
if ($user->avatar) {
    $avatarPath = $profile . '/' . $user->avatar;
    echo "<p><strong>مسار الصورة الكامل:</strong> " . $avatarPath . "</p>";
    
    // Check if file exists in storage
    $storagePath = storage_path('uploads/avatar/' . $user->avatar);
    echo "<p><strong>مسار الملف في storage:</strong> " . $storagePath . "</p>";
    echo "<p><strong>هل الملف موجود في storage:</strong> " . (file_exists($storagePath) ? 'نعم' : 'لا') . "</p>";
    
    // Check if file exists in public/storage
    $publicPath = public_path('storage/avatar/' . $user->avatar);
    echo "<p><strong>مسار الملف في public/storage:</strong> " . $publicPath . "</p>";
    echo "<p><strong>هل الملف موجود في public/storage:</strong> " . (file_exists($publicPath) ? 'نعم' : 'لا') . "</p>";
    
    // Display the image
    echo "<h3>معاينة الصورة:</h3>";
    echo "<img src='" . $avatarPath . "' style='max-width: 200px; border: 1px solid #ccc;' alt='صورة المستخدم'>";
} else {
    echo "<p><strong>لا توجد صورة محفوظة للمستخدم</strong></p>";
}

echo "<h3>قائمة الملفات في storage/uploads/avatar/:</h3>";
$storageFiles = glob(storage_path('uploads/avatar/*'));
foreach ($storageFiles as $file) {
    echo "<p>" . basename($file) . "</p>";
}

echo "<h3>قائمة الملفات في public/storage/avatar/:</h3>";
$publicFiles = glob(public_path('storage/avatar/*'));
foreach ($publicFiles as $file) {
    echo "<p>" . basename($file) . "</p>";
}
?>
