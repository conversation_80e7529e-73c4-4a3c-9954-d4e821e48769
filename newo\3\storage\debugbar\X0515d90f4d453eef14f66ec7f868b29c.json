{"__meta": {"id": "X0515d90f4d453eef14f66ec7f868b29c", "datetime": "2025-06-14 02:51:07", "utime": **********.145277, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.615565, "end": **********.145302, "duration": 1.****************, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": **********.615565, "relative_start": 0, "end": **********.927739, "relative_end": **********.927739, "duration": 1.***************, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.927767, "relative_start": 1.***************, "end": **********.145305, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017580000000000002, "accumulated_duration_str": "17.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0470889, "duration": 0.01408, "duration_str": "14.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.091}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.082735, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.091, "width_percent": 7.736}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1224709, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 87.827, "width_percent": 12.173}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; _clsk=1rbg52q%7C1749869464702%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFVcUhwb1kycmF4VkgzUkIrL2hCalE9PSIsInZhbHVlIjoiNWhMNHJLUDdGVUFyTHNVR3pHa0NZVlViaEdDWmF1UEdvYjJKTzZzeDlCSzFTbzE0Q1l0UWxqSFNGeWo1ODJqZVBhTDZUMjVJQVFmTWZyTzdvQ2NTSFovYlNSN2N0TlRHczNoNytOaktkTXB1ZmFMbEN6dHh0TlNOdmVmNjR0Mjc4T3ZnVlUwYSszQnJQUlhGMVAvV0ZRajRTTVQzbWpQb2w4cjEyMFNrY09rbENpaWZIN1hMeDRzL083Z2JUTUxJMjRJdzU1Tmh1L2ZEdGczTFBNWTlscmIrODVVZk9OU3hVMWZvSGlTZ1RBTUxyUDkycUlIaU1KeWh6SWxMZE9USVgxazNKTi8wUElpMHhvMjI0M1M3aFdZVmRORlFxT2YyY3ZTaXdIVlhudnVwRWdnT2pFa2MxQ3B2d0xDQmtvSUJwRzJSaUJFZTBkWmpkbUxkL2prcXZyWTVDczU5NHNwNklmeklSK0lKalFBNnB0RXU0cFpsTmk0WUhVTXlLU0VLUkJEbDVKcUswRkpZYTJhL1d6WGkwSDcyZlBYeVg0MnQvK2s0dm5od0VjMkNUQzJFRkpqVWlLTmxuRlR2MVNKRXBxR2h6YnNRZUdzdExGQzRlRGZ1d2gva0kyMXY1QURRZlNLRHM4dENEMWZXcVI0SE9CTFN5M1RzZmsxVFRlZXAiLCJtYWMiOiI1OGZkOTc3YTQ0OWMyZTViOWU2MzYzZWY2Mzg1ZDE0MmRmZjdhYzc1NDI2MmRkNDYxNTcxOWQxNWFmZjAwN2VlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpuZS92cHZiRTFpTDhWbGJsNXZTaEE9PSIsInZhbHVlIjoicTBPTkZBKzVTS3dUSjVwRWwrak42bk80N0xkZ0h4dFl2clRjUXpRakN2Rk1JSDVwa1NBa2JvZzR3eG1wSGpoYVBITktJSHV2SjE3dFpTYStsNjdxZTVaRFYyYml6YXNFd3o2ZVVBTjk4RDdrZ3hHMiswelhVU2NVRkh3Y21CRFd5VFhQT0J3UzNCSXdQK1g5TTIrMDdaSTZZVnZLMGxiRHpCWDFMSURaeFhZejhob1hYcXhvVHpzRnI3NFhYL095dFJFWTBMM3B5eDU0TnhLRDc4Tno3ajkyUUkxQW16NGRaa1VyTXpuMjF4Tk9QV3ByVFExRGt2dk1CWHpzdUM2enJkNzNHSlM0bmZUbVJ2b3M0S1NzQzZiUlBSZVpOdXU1aFJBTEdha0pxWTFuaTNhcUVyaTByVElPQ0pkMnBqTHF4WFhyWFF0aGJIZW8yZDFqdEw0d28xd1l2N1BOTUlSZmxlZU9sbmIzVHJlZmpyUld4TWg5bURwUk9iU05iUk8yZytYNmpKWFhTL1BwZXhXcG1RMVNDUTBzQi9YMWhYWUY0dGxjV3NFR3E0eGY3N1ozQmRsR1pyR0lJYTJYeHliZVl2VmR5anJPUUhVUnpkVDlWNUJReDdxU2dDVm15TVRUQStTVUFnWkYvTFMwOVhWdkpaRys0T1hOL3lzRzJFcDYiLCJtYWMiOiI0NmE5NDA0YzlmM2JlMGYxZDA3ZGZiMjJmNjBlNTgwMDJiMGViOWE5NGM4OTY2YmVhZmJmYTE5MzNkNmE3MjAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-298010033 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t6clpBhZQEC0tcIVAqApy3fwEvNxCUuY7Y8VEjFB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298010033\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1721201024 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 02:51:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNKaTJwSXFCNmRQRllSS2V5K0s0WWc9PSIsInZhbHVlIjoiSS9JcDRNT1U1ZGlReHdOaUZLeUpxRWxUTldaZXpYTmp5ZmozNWdZbkJ4YWloVHJFTWt2RE9EMFp1VVAzbFpZVEthMFgzektQTE9ZNTVMdEw5MWJvQjF0eXFmQTc4dU5kRzlScWxDMldtRVkxWFRBM3FydXVBMkF5NVhhY1VDeHZzck4ra09UMVdiRmlpekRpVmZCS0dIQlV6d3pNWXM5Rmg5ZVkwa3IybnpYQWFzd1pVNUJicDJpd3h1UnUxZFBHeXlQTzBYR0pzK3J5TTdQbHZTMTlzVkVmTnlrbjdleHM0eE5sR2R5SVhucmJ6M2R4OUdYMzVPMjNyT29iSGVIWUpBWFpzQ3VIKzNqcG5PQ2xUTXA3dUdoTlpvbnkrUzhOdVVVTjRrUkMxMWNVbFpaSm1PVHZicnI1THRncHZBK2JjeDB3cG9mcnNGMlUzamRoM0Mrck9UbklOcjhpdWpUWk52SFhZdVNiNmxsN25mSmk0aE5YenY0S0pyRnBVQkVBYlQ4aFArYzJZSmtVRTRyMVF3Y21wdjd6bkRRME1BMGIzaTBBR2Nxd0N6WVExWkJkcXpxTSt0WmVTSGlSOWttZ05YWFZOd0o5dHh0S0RwUzBFb1U2anBPL1c1Q3FUTTRuVEo0NkVETGM4RW1QWkFaMXlXUERPTkdRSEdUNkhHZlYiLCJtYWMiOiJmN2JlYjQ4M2FmYzA1YTZjNGQyNjU2ZWM4MTZkNjdlMDdmMDhhNjUzOWQwZDNiMWQ1MDg1YzY4OGUxYWNhOTYwIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJaOEFTMzRQUThqeHJyNjNlWS9ROWc9PSIsInZhbHVlIjoiY2V2UDlxcnJpdFlOeXRTeVRBeWV6cVJxZ29CMVNpaTZDaEFkRnlibWtHZC83ckZFd0VoNlRUMjBEQ2U0cDE1ckRUUzdHZzVOZkZrVVcvM3lqeVp6ZjZ2bUJWbUJtQ3ZQVDJaUmRXSHMvYWFvZU8wdU9KL01aVVIxTHluZjRiaTl3SWV1eUtpU1JsaDd0a0QrdDBkN0Q2ZXRMcW5JS3o4SjQ1WEIvRENiTzNBdkJobFEvaW9WN0c3R0hOUmtWYjl4T3d2SlpxdDUzZ3Yzc0lvdjMzWVM2dmI4RkE4K2kwUEYxNEtiS2h1K2V0YnRXbWlkOGZDTXdyeDBVV1p1YXhkcXpnZEpxWWVSTG5kRktRaks2bWlVdUNadTlUSEM0RmVxZWFlSFQ1eXBHYk1OVEJMUkYzanBwVDVETDY5MmpzQ0ZxQjVYTU5MZFBWa0xPWDhxMmNQcUs4ekJkYXhDSTJFaUJhSDUxTzdHUHBkaDZqTExXdWhmNWRkWkZGYmFLK2F0N1dGa1FidUpCSDk3dGliZkxYVkQ5K2hQcmpmSEFhN0RkS2FFdGpsSkkxS0tFVVBTNlVMQ2VTMG1jTE1jb2RrVzNNS0FXWllFTWdYdldCZk9TalU3OGdFeEdpcXBOS2x2eTlRVmhhZnY5LzA0K1c1VnpoT3hsNzROV2JNNTRtbnMiLCJtYWMiOiI5ZDRmYzU0Nzg4N2I1ZDUyZmQ2Y2U5MDExMjJmY2UxYTIwMzc2M2QwNGZkNDZkYmZmY2ZkMzkyMGI2NjBiMTg0IiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 04:51:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNKaTJwSXFCNmRQRllSS2V5K0s0WWc9PSIsInZhbHVlIjoiSS9JcDRNT1U1ZGlReHdOaUZLeUpxRWxUTldaZXpYTmp5ZmozNWdZbkJ4YWloVHJFTWt2RE9EMFp1VVAzbFpZVEthMFgzektQTE9ZNTVMdEw5MWJvQjF0eXFmQTc4dU5kRzlScWxDMldtRVkxWFRBM3FydXVBMkF5NVhhY1VDeHZzck4ra09UMVdiRmlpekRpVmZCS0dIQlV6d3pNWXM5Rmg5ZVkwa3IybnpYQWFzd1pVNUJicDJpd3h1UnUxZFBHeXlQTzBYR0pzK3J5TTdQbHZTMTlzVkVmTnlrbjdleHM0eE5sR2R5SVhucmJ6M2R4OUdYMzVPMjNyT29iSGVIWUpBWFpzQ3VIKzNqcG5PQ2xUTXA3dUdoTlpvbnkrUzhOdVVVTjRrUkMxMWNVbFpaSm1PVHZicnI1THRncHZBK2JjeDB3cG9mcnNGMlUzamRoM0Mrck9UbklOcjhpdWpUWk52SFhZdVNiNmxsN25mSmk0aE5YenY0S0pyRnBVQkVBYlQ4aFArYzJZSmtVRTRyMVF3Y21wdjd6bkRRME1BMGIzaTBBR2Nxd0N6WVExWkJkcXpxTSt0WmVTSGlSOWttZ05YWFZOd0o5dHh0S0RwUzBFb1U2anBPL1c1Q3FUTTRuVEo0NkVETGM4RW1QWkFaMXlXUERPTkdRSEdUNkhHZlYiLCJtYWMiOiJmN2JlYjQ4M2FmYzA1YTZjNGQyNjU2ZWM4MTZkNjdlMDdmMDhhNjUzOWQwZDNiMWQ1MDg1YzY4OGUxYWNhOTYwIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJaOEFTMzRQUThqeHJyNjNlWS9ROWc9PSIsInZhbHVlIjoiY2V2UDlxcnJpdFlOeXRTeVRBeWV6cVJxZ29CMVNpaTZDaEFkRnlibWtHZC83ckZFd0VoNlRUMjBEQ2U0cDE1ckRUUzdHZzVOZkZrVVcvM3lqeVp6ZjZ2bUJWbUJtQ3ZQVDJaUmRXSHMvYWFvZU8wdU9KL01aVVIxTHluZjRiaTl3SWV1eUtpU1JsaDd0a0QrdDBkN0Q2ZXRMcW5JS3o4SjQ1WEIvRENiTzNBdkJobFEvaW9WN0c3R0hOUmtWYjl4T3d2SlpxdDUzZ3Yzc0lvdjMzWVM2dmI4RkE4K2kwUEYxNEtiS2h1K2V0YnRXbWlkOGZDTXdyeDBVV1p1YXhkcXpnZEpxWWVSTG5kRktRaks2bWlVdUNadTlUSEM0RmVxZWFlSFQ1eXBHYk1OVEJMUkYzanBwVDVETDY5MmpzQ0ZxQjVYTU5MZFBWa0xPWDhxMmNQcUs4ekJkYXhDSTJFaUJhSDUxTzdHUHBkaDZqTExXdWhmNWRkWkZGYmFLK2F0N1dGa1FidUpCSDk3dGliZkxYVkQ5K2hQcmpmSEFhN0RkS2FFdGpsSkkxS0tFVVBTNlVMQ2VTMG1jTE1jb2RrVzNNS0FXWllFTWdYdldCZk9TalU3OGdFeEdpcXBOS2x2eTlRVmhhZnY5LzA0K1c1VnpoT3hsNzROV2JNNTRtbnMiLCJtYWMiOiI5ZDRmYzU0Nzg4N2I1ZDUyZmQ2Y2U5MDExMjJmY2UxYTIwMzc2M2QwNGZkNDZkYmZmY2ZkMzkyMGI2NjBiMTg0IiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 04:51:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721201024\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-44612819 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44612819\", {\"maxDepth\":0})</script>\n"}}