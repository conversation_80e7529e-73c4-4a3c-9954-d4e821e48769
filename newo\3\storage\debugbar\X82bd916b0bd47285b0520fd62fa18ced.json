{"__meta": {"id": "X82bd916b0bd47285b0520fd62fa18ced", "datetime": "2025-06-14 03:08:32", "utime": **********.211843, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749870510.867501, "end": **********.211882, "duration": 1.3443810939788818, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749870510.867501, "relative_start": 0, "end": **********.07232, "relative_end": **********.07232, "duration": 1.2048189640045166, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.072369, "relative_start": 1.2048680782318115, "end": **********.211886, "relative_end": 3.814697265625e-06, "duration": 0.13951683044433594, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43905720, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00749, "accumulated_duration_str": "7.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.156998, "duration": 0.00637, "duration_str": "6.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.047}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.177523, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 85.047, "width_percent": 14.953}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1694542277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1694542277\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1020947679 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1020947679\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1340090694 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwr%7C0%7C1982; XSRF-TOKEN=eyJpdiI6InA2V3hoREhhWnZjUmQxN3lpUmxqVkE9PSIsInZhbHVlIjoiYlI2VlZZMGVkRjd6RjlYaFlYdDBkaXBnc0RFdk5QMHdBa0YxMzFJUGF6b1QxbkNvWWs5TDhVb2NoYUZyeWx1cHdodGdCSTF4bWpJL0xMb3BlNGFHbEhvNndVUWxsZWlnejNFeEY2cGo2TEk2ZDkxK0NpQllkaER0aFFrYjRxcWxaSHBWQk4rY3U2Vit3amQrTHFsT01lQ0htVVpDRVA2YmlWcEIyZGRReW1pdmxsUU1KVGthSUtJeUorZXppRTJ6YTRYSXRQQkUwVEIwTzRURkpQbUdBQi8reXlwcG9FcWIrbzdpMVpIT1ZWV1ZiVVEwTnFDcHdUb2lMMzMwa1pOUGR2QnYwby9venFsUHZabHJ3OGNvdlgxN1VHeklZK1dyZklwTGVOb2V4QmNmeVlEdnVZc2d4VE1ta254YWcrNmhnbkF0OWtTL0NNRmFrY3N2dUlIemV3SU5TNnh6bnFDRjBMbTBpUVRENGNsWFB2WG9pb1JQZVpFTTRKM0pRR0hER2E1UzhvaWNmbUw1eXFXZUdnZmQxY0tVUnc1SytzczVwVW9MT3J2bjI3NTluVmFWZ05udGtlR3QwSU1BT3NNK0IyNEorcjRMYWREUkFPSysxd1N2OVhubCtZbUVMeWxZRW5qV1QyMlplN1ZzRXI4Ri9UeXllS3B0ckM3QnZVQlIiLCJtYWMiOiJlMjY3MjZmYjgwYmMwYzNiN2VmOTNjZmYzZThmZTM5MWY1OWEzMWI0NTc2OGYzNzMyMGI5ODM0YmI5NjM0OTE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlMvU3NqQThWenA3VWdiU1lUWHpGVEE9PSIsInZhbHVlIjoiZ2owalZMdEYyUk4xdzYvR0RuR2NlRWtDTDZvOUhMOWZxdTN2Rnd0aDhQTmc5eCszc2ovejE4VzMyOWlmL2Q0T0haTnVsM0FORTMwazk2dEo1OHF5SHp4YUJXTXdvQnlxbDRSRjlzc25OTnplcHJFdFhwSTQ2VnBNL1JHREh0d0xlMWJwOHRkZU9wM1VFMzhLMlI4RFE3RDdERU13dU5jR1c5SnJEdFFkNENyYktHZ3JqM3JiRk9rMmJhYXhCQVpBTHovWXdEUi9odkpCTnlrVDVwM3NYNUdrei9yUnMzUm5GN3BJcE53Q08yWkpHMnhPcGlUWWxEVng3blRqaFRYQzJSUGk2K3ZUczlTUFBGblhQMWRZNU9PdkhMN29rRUdlZ2RNNU0xaCtBYzRQUC9rQkxnNG12ZUxaSGlkOHd6MitxNVhZOHZsWTdOWVdnMGw0SGZPNzRKc0YvRzZQUnRVQzhMRTVQcUlrK0xRaTNlZ3YrNkJOU1Q1OGJVL0duc0tZcDF6elY0dGxyQ0dISVIvVnJabWdXLzJ6YWFERDZtN3VqREhJQjhnWE00YUYwUkgvSWVuZHhGN3BEUWJGRmtqVGs3NmJsbmYvR0pldmpnVGZFT3FmNDBGZ1k5YzlYa0ZLYXJ4ZlNjdVlvRzhrZ1gvS2VnQ2UzeFVtYU54QzFKbDQiLCJtYWMiOiJhNjM0MmJhM2VmMzQwMDMzOTM1OTVjZTBlMzA4YzBkMzg5MWZlZmFiZTg5NDUwNjBjNTdiODE2ZmY4MmU3NDkzIiwidGFnIjoiIn0%3D; _clsk=1rbg52q%7C1749870508641%7C4%7C1%7Cq.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340090694\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1088212551 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t6clpBhZQEC0tcIVAqApy3fwEvNxCUuY7Y8VEjFB</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088212551\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-712410246 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 03:08:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik8vZVdDMXcya1BFZzVpRDlLNzZsN1E9PSIsInZhbHVlIjoiZGFrZjNpa3RlcU5kaHA1U3BXeXpXTVNjcmp5V1d4SkpDTjNNVzhDaTB5Q1dsMURiNHliWm1LTTR1Q3A4M3lidUZpdURycVNlbnVab2FwTmdSdGJESSswT3B6OWx5UzNHbTN5dVRtQXVVa2F3SFpzZUpsQWhkcWoraGpXT0xQTm5IalhGY0FJRE1tVG9mVmJSM1UvYXRQWUNNSFRqcHVFVUdRZDRxVXJNYTR2TUlGS3FFVllpeFpWc3RGZHNVbHBZd3U3QklIVVZHeHQ5aUlZbW9VamZkOEd6L2EyQnltSWVPYll1TnNtdTVrQjJuSlJjcjFsWXVRNnVKZWxqL2R3UG1hL3JWbkswRUE3aXZvUnluMmVMdldoS3hFeVNYL0hDSDRjTm5KbXJjRjczRm1oMUJpN2ZhTHpxNnBPUVpGUW9KTkFtZW1iVWlWT2J5Nms1QzZ5M3ZvUHhUaUxUNzJDaUFLekdxbnJjbWZrT1BZRzNJRnN1M285bmhsRExtN2E1ZklyWDN5dzhMd3JJVngrcFo2cWJZcFR1RFNYT0ZkcTZNSW5hNkNMN2IwaE5DOWpyU3lscXVobG9WdlRSNmlwc2RJWXY2SHg3YjBrT1YrVEVaV3R2Yi9HYmtMVllIRm9YZmVleWd5MlBlMExXR0ZHUXB2ZmNaTmNzWTlicjU1b2UiLCJtYWMiOiI0N2ZhZWRmOTFlMDg2YmY4NzhkNzU0NTQ2NWZlNGJmZjMwMjgyYWRjMzIzM2U2MWVlYjZkZjcxYzZmYmRiYThlIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 05:08:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVDQ0ZXV3pldTZyQUFSd0RXZFdlNVE9PSIsInZhbHVlIjoiY2o5TWxiTmpzUmhNa1daaWEzNisxZVZtbjk5VFgwR01iT2FrcEUxTk9sSTMveTJSOEVQT2dMWXlwVFJpM05ST05vRXBCd2F6NVYyRkE3ZmQwWGJQS2RPdndoZWFLS2hiWHYweklaR1FCRWxtYUdDZnhZemJsd1pFdmhGd1J1K2k5MU1FSnk5UUl6Njl6UmFuQTQ4VFpHeXA5K3VyYy9WZUVGMWdwa0NTQXdTdTlxWmV2bkthZDM5OGRPdWZzQkxKUWRtZFpDdWZYUHhjeGRpUlIxZmZOaWpva0trYWxOdFhKaS9zc1dPTnVKVWVOeWlvUDdUWWExSGNPUmNkS0RERDZFUzFDMi9TcVd0b0Y1RzhBd0U0eEE0eWVIRTJXanM0dXRhQ2RHZU9scnZxdHV1TnloTVh4MEUreWQrNS9GVHIzcnN6QVBnMUlyUW56Sit4S3Nod0FNV3p0ZHN2V0pvdVZRQTVHSXYzTVF2VkFmajd3MTR0dFZDQmY3QStrNUNOdG55ZFY2dXRGdE1mUTdLeUFpVFJLZEg0SzB2OVdJUzdkK0YyS2ZzblAxTGJxZlVwWmpLajE4ZVpoUENpRFFqN1lKdzVNRTBXL0paVEdXUFRnd2tJVHlWZnFFeTNBVmpobnlCcGJBMi90Y0JTUkMyZXo5aHdGVGg5Tkx2T1AycjciLCJtYWMiOiJlMGZlNTM2N2E2NTE5MTI1YmZhMjg5Yzg5MmU1MGNkMjJjYzRlZDEwMDVkN2UxNGE1YzQ1MWM4MjczMWRiMzEwIiwidGFnIjoiIn0%3D; expires=Sat, 14 Jun 2025 05:08:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik8vZVdDMXcya1BFZzVpRDlLNzZsN1E9PSIsInZhbHVlIjoiZGFrZjNpa3RlcU5kaHA1U3BXeXpXTVNjcmp5V1d4SkpDTjNNVzhDaTB5Q1dsMURiNHliWm1LTTR1Q3A4M3lidUZpdURycVNlbnVab2FwTmdSdGJESSswT3B6OWx5UzNHbTN5dVRtQXVVa2F3SFpzZUpsQWhkcWoraGpXT0xQTm5IalhGY0FJRE1tVG9mVmJSM1UvYXRQWUNNSFRqcHVFVUdRZDRxVXJNYTR2TUlGS3FFVllpeFpWc3RGZHNVbHBZd3U3QklIVVZHeHQ5aUlZbW9VamZkOEd6L2EyQnltSWVPYll1TnNtdTVrQjJuSlJjcjFsWXVRNnVKZWxqL2R3UG1hL3JWbkswRUE3aXZvUnluMmVMdldoS3hFeVNYL0hDSDRjTm5KbXJjRjczRm1oMUJpN2ZhTHpxNnBPUVpGUW9KTkFtZW1iVWlWT2J5Nms1QzZ5M3ZvUHhUaUxUNzJDaUFLekdxbnJjbWZrT1BZRzNJRnN1M285bmhsRExtN2E1ZklyWDN5dzhMd3JJVngrcFo2cWJZcFR1RFNYT0ZkcTZNSW5hNkNMN2IwaE5DOWpyU3lscXVobG9WdlRSNmlwc2RJWXY2SHg3YjBrT1YrVEVaV3R2Yi9HYmtMVllIRm9YZmVleWd5MlBlMExXR0ZHUXB2ZmNaTmNzWTlicjU1b2UiLCJtYWMiOiI0N2ZhZWRmOTFlMDg2YmY4NzhkNzU0NTQ2NWZlNGJmZjMwMjgyYWRjMzIzM2U2MWVlYjZkZjcxYzZmYmRiYThlIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 05:08:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVDQ0ZXV3pldTZyQUFSd0RXZFdlNVE9PSIsInZhbHVlIjoiY2o5TWxiTmpzUmhNa1daaWEzNisxZVZtbjk5VFgwR01iT2FrcEUxTk9sSTMveTJSOEVQT2dMWXlwVFJpM05ST05vRXBCd2F6NVYyRkE3ZmQwWGJQS2RPdndoZWFLS2hiWHYweklaR1FCRWxtYUdDZnhZemJsd1pFdmhGd1J1K2k5MU1FSnk5UUl6Njl6UmFuQTQ4VFpHeXA5K3VyYy9WZUVGMWdwa0NTQXdTdTlxWmV2bkthZDM5OGRPdWZzQkxKUWRtZFpDdWZYUHhjeGRpUlIxZmZOaWpva0trYWxOdFhKaS9zc1dPTnVKVWVOeWlvUDdUWWExSGNPUmNkS0RERDZFUzFDMi9TcVd0b0Y1RzhBd0U0eEE0eWVIRTJXanM0dXRhQ2RHZU9scnZxdHV1TnloTVh4MEUreWQrNS9GVHIzcnN6QVBnMUlyUW56Sit4S3Nod0FNV3p0ZHN2V0pvdVZRQTVHSXYzTVF2VkFmajd3MTR0dFZDQmY3QStrNUNOdG55ZFY2dXRGdE1mUTdLeUFpVFJLZEg0SzB2OVdJUzdkK0YyS2ZzblAxTGJxZlVwWmpLajE4ZVpoUENpRFFqN1lKdzVNRTBXL0paVEdXUFRnd2tJVHlWZnFFeTNBVmpobnlCcGJBMi90Y0JTUkMyZXo5aHdGVGg5Tkx2T1AycjciLCJtYWMiOiJlMGZlNTM2N2E2NTE5MTI1YmZhMjg5Yzg5MmU1MGNkMjJjYzRlZDEwMDVkN2UxNGE1YzQ1MWM4MjczMWRiMzEwIiwidGFnIjoiIn0%3D; expires=Sat, 14-Jun-2025 05:08:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712410246\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1486097960 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJWAEYGSAVQqs7EdbLr0zNn5F0n1tGdHf7bFPBAg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486097960\", {\"maxDepth\":0})</script>\n"}}