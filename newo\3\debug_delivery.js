/**
 * ملف Debug لاختبار نظام التوصيل
 * 
 * افتح Developer Tools في المتصفح وانسخ هذا الكود في Console
 */

console.log('=== Debug نظام التوصيل ===');

// 1. فحص وجود الزر
function checkDeliveryButton() {
    const paymentBtn = document.getElementById('payment');
    if (paymentBtn) {
        console.log('✅ زر الدفع موجود');
        console.log('النص الحالي:', paymentBtn.textContent);
        console.log('الخصائص:', {
            'data-url': paymentBtn.getAttribute('data-url'),
            'data-delivery-url': paymentBtn.getAttribute('data-delivery-url'),
            'data-ajax-popup': paymentBtn.getAttribute('data-ajax-popup'),
            'class': paymentBtn.className
        });
        return paymentBtn;
    } else {
        console.log('❌ زر الدفع غير موجود');
        return null;
    }
}

// 2. فحص بيانات العميل
function checkCustomerData() {
    const customerId = document.getElementById('customer')?.value;
    const customerHidden = document.getElementById('vc_name_hidden')?.value;
    
    console.log('بيانات العميل:', {
        'customer_id': customerId,
        'vc_name_hidden': customerHidden
    });
    
    return customerId && customerHidden;
}

// 3. فحص بيانات المستودع
function checkWarehouseData() {
    const warehouseId = document.getElementById('warehouse')?.value;
    const warehouseHidden = document.getElementById('warehouse_name_hidden')?.value;
    
    console.log('بيانات المستودع:', {
        'warehouse_id': warehouseId,
        'warehouse_name_hidden': warehouseHidden
    });
    
    return warehouseId && warehouseHidden;
}

// 4. فحص السلة
function checkCart() {
    const cartRows = document.querySelectorAll('#tbody tr:not(.no-found)');
    console.log('عدد المنتجات في السلة:', cartRows.length);
    
    if (cartRows.length > 0) {
        console.log('✅ السلة تحتوي على منتجات');
        return true;
    } else {
        console.log('❌ السلة فارغة');
        return false;
    }
}

// 5. فحص CSRF Token
function checkCSRFToken() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
        console.log('✅ CSRF Token موجود');
        return csrfToken;
    } else {
        console.log('❌ CSRF Token غير موجود');
        return null;
    }
}

// 6. محاكاة طلب AJAX
function simulateDeliveryRequest() {
    const customerId = document.getElementById('customer')?.value;
    const warehouseId = document.getElementById('warehouse_name_hidden')?.value;
    const csrfToken = checkCSRFToken();
    
    if (!customerId || !warehouseId || !csrfToken) {
        console.log('❌ بيانات مفقودة للطلب');
        return;
    }
    
    const requestData = {
        customer_id: customerId,
        warehouse_name: warehouseId,
        user_id: document.getElementById('delivery_user_hidden')?.value || '',
        discount: document.getElementById('discount_hidden')?.value || 0,
        quotation_id: document.getElementById('quotation_id')?.value || 0,
    };
    
    console.log('بيانات الطلب:', requestData);
    
    // محاكاة الطلب (بدون إرسال فعلي)
    console.log('URL الطلب:', window.location.origin + '/pos/store/delivery');
    console.log('Headers:', {
        'X-CSRF-TOKEN': csrfToken,
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json'
    });
    
    return requestData;
}

// 7. اختبار شامل
function runFullTest() {
    console.log('\n=== اختبار شامل ===');
    
    const button = checkDeliveryButton();
    const customerOK = checkCustomerData();
    const warehouseOK = checkWarehouseData();
    const cartOK = checkCart();
    const csrfOK = checkCSRFToken();
    
    console.log('\nنتائج الفحص:');
    console.log('الزر:', button ? '✅' : '❌');
    console.log('العميل:', customerOK ? '✅' : '❌');
    console.log('المستودع:', warehouseOK ? '✅' : '❌');
    console.log('السلة:', cartOK ? '✅' : '❌');
    console.log('CSRF:', csrfOK ? '✅' : '❌');
    
    if (button && customerOK && warehouseOK && cartOK && csrfOK) {
        console.log('\n✅ جميع المتطلبات متوفرة');
        console.log('يمكنك الآن اختبار الطلب باستخدام: simulateDeliveryRequest()');
    } else {
        console.log('\n❌ بعض المتطلبات مفقودة');
    }
}

// 8. مراقب الأحداث
function addEventMonitor() {
    const paymentBtn = document.getElementById('payment');
    if (paymentBtn) {
        paymentBtn.addEventListener('click', function(e) {
            console.log('تم النقر على زر الدفع');
            console.log('Event:', e);
            console.log('Target:', e.target);
            console.log('Classes:', e.target.className);
        });
        console.log('✅ تم إضافة مراقب الأحداث');
    }
}

// تشغيل الاختبار التلقائي
runFullTest();
addEventMonitor();

console.log('\n=== الأوامر المتاحة ===');
console.log('runFullTest() - اختبار شامل');
console.log('checkDeliveryButton() - فحص الزر');
console.log('checkCustomerData() - فحص بيانات العميل');
console.log('checkWarehouseData() - فحص بيانات المستودع');
console.log('checkCart() - فحص السلة');
console.log('simulateDeliveryRequest() - محاكاة الطلب');
console.log('addEventMonitor() - إضافة مراقب الأحداث');

console.log('\n=== انتهى Debug ===');
