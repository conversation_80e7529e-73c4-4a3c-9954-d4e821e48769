#!/bin/bash

# =============================================================================
# سكريبت نشر صلاحية عرض جميع أوامر الاستلام للمستخدم Company
# =============================================================================

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# إعدادات الخادم (يجب تعديلها حسب بيئتك)
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"

# دالة لطباعة الحالة
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# دالة لطباعة النجاح
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# دالة لطباعة الخطأ
print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# دالة لطباعة التحذير
print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# دالة للتحقق من نجاح العملية
check_success() {
    if [ $? -eq 0 ]; then
        print_success "$1 تم بنجاح"
    else
        print_error "فشل في $1"
        exit 1
    fi
}

# بداية السكريبت
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}  نشر صلاحية Company لأوامر الاستلام  ${NC}"
echo -e "${BLUE}================================${NC}"
echo ""

# التحقق من وجود الملفات المطلوبة
print_status "التحقق من وجود الملفات المطلوبة..."

required_files=(
    "app/Http/Controllers/ReceiptOrderController.php"
    "resources/views/partials/admin/menu.blade.php"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "الملف غير موجود: $file"
        exit 1
    fi
done

print_success "جميع الملفات المطلوبة موجودة"

# التحقق من التغييرات المطلوبة
print_status "التحقق من التغييرات المطلوبة..."

# البحث عن التغييرات الجديدة في الكونترولر
if grep -q "hasRole('company')" app/Http/Controllers/ReceiptOrderController.php; then
    print_success "تغييرات الكونترولر موجودة"
else
    print_error "تغييرات الكونترولر غير موجودة"
    exit 1
fi

# البحث عن التغييرات الجديدة في القائمة
if grep -q "أوامر الاستلام.*إدارة العمليات المالية" resources/views/partials/admin/menu.blade.php; then
    print_success "تغييرات القائمة الجانبية موجودة"
else
    print_error "تغييرات القائمة الجانبية غير موجودة"
    exit 1
fi

# المرحلة 1: إنشاء نسخة احتياطية
print_status "🔄 المرحلة 1: إنشاء نسخة احتياطية..."
ssh $SERVER_USER@$SERVER_HOST "cp $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php.backup.$(date +%Y%m%d_%H%M%S)"
ssh $SERVER_USER@$SERVER_HOST "cp $PROJECT_PATH/resources/views/partials/admin/menu.blade.php $PROJECT_PATH/resources/views/partials/admin/menu.blade.php.backup.$(date +%Y%m%d_%H%M%S)"
check_success "إنشاء نسخة احتياطية"

# المرحلة 2: رفع الملفات المحدثة
print_status "🚀 المرحلة 2: رفع الملفات المحدثة..."

# رفع الكونترولر
print_status "رفع الكونترولر..."
scp app/Http/Controllers/ReceiptOrderController.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Http/Controllers/
check_success "رفع الكونترولر"

# رفع ملف القائمة الجانبية
print_status "رفع ملف القائمة الجانبية..."
scp resources/views/partials/admin/menu.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/partials/admin/
check_success "رفع ملف القائمة الجانبية"

# المرحلة 3: ضبط الصلاحيات
print_status "🔐 المرحلة 3: ضبط صلاحيات الملفات..."
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php"
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/resources/views/partials/admin/menu.blade.php"
check_success "ضبط صلاحيات الملفات"

# المرحلة 4: مسح الكاش
print_status "🧹 المرحلة 4: مسح الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan cache:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan view:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:clear"
check_success "مسح الكاش"

# المرحلة 5: إعادة تحميل الكاش (اختياري)
print_status "⚡ المرحلة 5: إعادة تحميل الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:cache"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan config:cache"
check_success "إعادة تحميل الكاش"

# انتهاء النشر
echo ""
echo -e "${GREEN}================================${NC}"
echo -e "${GREEN}       تم النشر بنجاح! 🎉        ${NC}"
echo -e "${GREEN}================================${NC}"
echo ""

print_success "تم نشر صلاحية Company لأوامر الاستلام بنجاح"
print_warning "يرجى اختبار النظام للتأكد من عمل جميع الوظائف"

echo ""
echo -e "${BLUE}📋 التحسينات المطبقة:${NC}"
echo "✅ إضافة صلاحية عرض جميع أوامر الاستلام للمستخدم company"
echo "✅ إضافة عنصر أوامر الاستلام في قسم إدارة العمليات المالية"
echo "✅ الحفاظ على صلاحيات المستخدمين الآخرين"
echo "✅ تحسين المراقبة الإدارية للنظام"

echo ""
echo -e "${BLUE}🧪 خطوات الاختبار:${NC}"
echo "1. تسجيل الدخول بحساب لديه دور company"
echo "2. التحقق من ظهور 'أوامر الاستلام' في قسم إدارة العمليات المالية"
echo "3. اختبار عرض جميع أوامر الاستلام في النظام"
echo "4. اختبار عرض تفاصيل أي أمر استلام"
echo "5. تسجيل الدخول بحساب Cashier والتأكد من عرض أوامره فقط"

echo ""
echo -e "${YELLOW}⚠️  ملاحظة مهمة:${NC}"
echo "المستخدم company سيرى جميع أوامر الاستلام في النظام"
echo "المستخدمون الآخرون سيرون أوامرهم فقط كما هو الحال سابقاً"

echo ""
print_status "انتهى النشر بنجاح! ✨"
