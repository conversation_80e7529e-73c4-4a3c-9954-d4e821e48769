<?php

// ملف تشخيص لاختبار إنشاء المنتجات
// يجب تشغيله من خلال المتصفح: /debug_product_creation.php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// تحميل Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// محاكاة طلب HTTP
$request = Request::create('/debug', 'GET');
$response = $kernel->handle($request);

// بدء Laravel
$app->boot();

echo "<h1>تشخيص إنشاء المنتجات</h1>";

try {
    // التحقق من الاتصال بقاعدة البيانات
    echo "<h2>1. اختبار الاتصال بقاعدة البيانات</h2>";
    $pdo = DB::connection()->getPdo();
    echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
    
    // التحقق من وجود الجداول المطلوبة
    echo "<h2>2. التحقق من الجداول</h2>";
    $tables = [
        'product_services',
        'product_service_categories', 
        'product_service_units',
        'chart_of_accounts',
        'taxes'
    ];
    
    foreach ($tables as $table) {
        try {
            $count = DB::table($table)->count();
            echo "✅ جدول {$table}: {$count} سجل<br>";
        } catch (Exception $e) {
            echo "❌ خطأ في جدول {$table}: " . $e->getMessage() . "<br>";
        }
    }
    
    // التحقق من بيانات المستخدم
    echo "<h2>3. بيانات المستخدم</h2>";
    if (Auth::check()) {
        $user = Auth::user();
        echo "✅ المستخدم مسجل الدخول: " . $user->name . " (ID: " . $user->id . ")<br>";
        echo "✅ Creator ID: " . $user->creatorId() . "<br>";
        echo "✅ النوع: " . $user->type . "<br>";
        
        // التحقق من الصلاحيات
        if ($user->can('create product & service')) {
            echo "✅ لديه صلاحية إنشاء المنتجات<br>";
        } else {
            echo "❌ ليس لديه صلاحية إنشاء المنتجات<br>";
        }
    } else {
        echo "❌ المستخدم غير مسجل الدخول<br>";
    }
    
    // التحقق من البيانات المطلوبة
    echo "<h2>4. البيانات المطلوبة لإنشاء منتج</h2>";
    
    // الفئات
    $categories = App\Models\ProductServiceCategory::where('type', 'product & service')->count();
    echo "الفئات المتاحة: {$categories}<br>";
    if ($categories == 0) {
        echo "❌ لا توجد فئات متاحة - يجب إنشاء فئة أولاً<br>";
    }
    
    // الوحدات
    $units = App\Models\ProductServiceUnit::count();
    echo "الوحدات المتاحة: {$units}<br>";
    if ($units == 0) {
        echo "❌ لا توجد وحدات متاحة - يجب إنشاء وحدة أولاً<br>";
    }
    
    // حسابات الإيرادات
    $incomeAccounts = App\Models\ChartOfAccount::leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
        ->where('chart_of_account_types.name', 'income')
        ->count();
    echo "حسابات الإيرادات: {$incomeAccounts}<br>";
    if ($incomeAccounts == 0) {
        echo "❌ لا توجد حسابات إيرادات متاحة<br>";
    }
    
    // حسابات المصروفات
    $expenseAccounts = App\Models\ChartOfAccount::leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
        ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
        ->count();
    echo "حسابات المصروفات: {$expenseAccounts}<br>";
    if ($expenseAccounts == 0) {
        echo "❌ لا توجد حسابات مصروفات متاحة<br>";
    }
    
    // اختبار إنشاء منتج تجريبي
    echo "<h2>5. اختبار إنشاء منتج تجريبي</h2>";
    
    if (Auth::check() && $categories > 0 && $units > 0 && $incomeAccounts > 0 && $expenseAccounts > 0) {
        try {
            $user = Auth::user();
            
            // الحصول على أول فئة ووحدة وحسابات متاحة
            $category = App\Models\ProductServiceCategory::where('type', 'product & service')
                ->where('created_by', $user->creatorId())
                ->first();
            
            $unit = App\Models\ProductServiceUnit::where('created_by', $user->creatorId())
                ->first();
            
            $incomeAccount = App\Models\ChartOfAccount::leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->where('chart_of_account_types.name', 'income')
                ->where('chart_of_accounts.created_by', $user->creatorId())
                ->first();
            
            $expenseAccount = App\Models\ChartOfAccount::leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
                ->where('chart_of_accounts.created_by', $user->creatorId())
                ->first();
            
            if ($category && $unit && $incomeAccount && $expenseAccount) {
                // إنشاء منتج تجريبي
                $testProduct = new App\Models\ProductService();
                $testProduct->name = 'منتج تجريبي - ' . date('Y-m-d H:i:s');
                $testProduct->sku = 'TEST-' . time();
                $testProduct->sale_price = 100.00;
                $testProduct->purchase_price = 80.00;
                $testProduct->quantity = 10;
                $testProduct->type = 'product';
                $testProduct->category_id = $category->id;
                $testProduct->unit_id = $unit->id;
                $testProduct->sale_chartaccount_id = $incomeAccount->id;
                $testProduct->expense_chartaccount_id = $expenseAccount->id;
                $testProduct->created_by = $user->creatorId();
                $testProduct->tax_id = '';
                $testProduct->description = 'منتج تجريبي لاختبار النظام';
                
                $testProduct->save();
                
                echo "✅ تم إنشاء منتج تجريبي بنجاح (ID: {$testProduct->id})<br>";
                
                // التحقق من حفظ المنتج
                $savedProduct = App\Models\ProductService::find($testProduct->id);
                if ($savedProduct) {
                    echo "✅ تم التحقق من حفظ المنتج في قاعدة البيانات<br>";
                    echo "اسم المنتج: {$savedProduct->name}<br>";
                    echo "SKU: {$savedProduct->sku}<br>";
                } else {
                    echo "❌ لم يتم العثور على المنتج في قاعدة البيانات<br>";
                }
                
            } else {
                echo "❌ لا يمكن إنشاء منتج تجريبي - بيانات مفقودة:<br>";
                if (!$category) echo "- فئة مفقودة<br>";
                if (!$unit) echo "- وحدة مفقودة<br>";
                if (!$incomeAccount) echo "- حساب إيرادات مفقود<br>";
                if (!$expenseAccount) echo "- حساب مصروفات مفقود<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ خطأ في إنشاء المنتج التجريبي: " . $e->getMessage() . "<br>";
            echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
        }
    } else {
        echo "❌ لا يمكن إجراء اختبار إنشاء المنتج - متطلبات مفقودة<br>";
    }
    
    // اختبار route التحقق من SKU
    echo "<h2>6. اختبار route التحقق من SKU</h2>";
    try {
        $routeExists = Route::has('productservice.check.sku');
        if ($routeExists) {
            echo "✅ route التحقق من SKU موجود<br>";
        } else {
            echo "❌ route التحقق من SKU غير موجود<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في التحقق من route: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>7. معلومات إضافية</h2>";
echo "Laravel Version: " . app()->version() . "<br>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Database: " . config('database.default') . "<br>";
echo "Environment: " . app()->environment() . "<br>";

$kernel->terminate($request, $response);
?>
