<?php

/**
 * اختبار سريع لزر حفظ طلب التوصيل
 */

echo "=== اختبار زر حفظ طلب التوصيل ===\n\n";

// 1. فحص الراوت
echo "1. فحص الراوت:\n";
$webRoutes = file_get_contents('routes/web.php');
if (strpos($webRoutes, 'pos.store.delivery') !== false) {
    echo "✅ راوت pos.store.delivery موجود\n";
} else {
    echo "❌ راوت pos.store.delivery غير موجود\n";
}

// 2. فحص الكنترولر
echo "\n2. فحص الكنترولر:\n";
$controllerFile = 'app/Http/Controllers/PosController.php';
if (file_exists($controllerFile)) {
    $controllerContent = file_get_contents($controllerFile);
    if (strpos($controllerContent, 'storeDeliveryOrder') !== false) {
        echo "✅ دالة storeDeliveryOrder موجودة في PosController\n";
    } else {
        echo "❌ دالة storeDeliveryOrder غير موجودة في PosController\n";
    }
} else {
    echo "❌ ملف PosController غير موجود\n";
}

// 3. فحص JavaScript
echo "\n3. فحص JavaScript:\n";
$indexFile = 'resources/views/pos/index.blade.php';
if (file_exists($indexFile)) {
    $indexContent = file_get_contents($indexFile);
    if (strpos($indexContent, 'delivery-order-btn') !== false) {
        echo "✅ معالج JavaScript للتوصيل موجود\n";
    } else {
        echo "❌ معالج JavaScript للتوصيل غير موجود\n";
    }
    
    if (strpos($indexContent, 'data-delivery-url') !== false) {
        echo "✅ خاصية data-delivery-url موجودة\n";
    } else {
        echo "❌ خاصية data-delivery-url غير موجودة\n";
    }
} else {
    echo "❌ ملف pos/index.blade.php غير موجود\n";
}

// 4. فحص قاعدة البيانات
echo "\n4. فحص قاعدة البيانات:\n";
try {
    // فحص إذا كان Laravel متاح
    if (class_exists('Illuminate\Support\Facades\Schema')) {
        echo "✅ Laravel متاح\n";
        
        // يمكن إضافة فحوصات قاعدة البيانات هنا إذا لزم الأمر
        echo "ℹ️  لفحص قاعدة البيانات، استخدم: php artisan tinker\n";
        echo "ℹ️  ثم: \\App\\Models\\Pos::where('delivery_status', 'delivery_pending')->count()\n";
    } else {
        echo "❌ Laravel غير متاح في هذا السياق\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage() . "\n";
}

// 5. نصائح للاختبار
echo "\n5. نصائح للاختبار:\n";
echo "✅ تأكد من وجود عميل لديه صلاحية delivery (is_delivery = 1)\n";
echo "✅ تأكد من وجود وردية مفتوحة\n";
echo "✅ تأكد من وجود منتجات في السلة\n";
echo "✅ تأكد من اختيار المستودع\n";
echo "✅ افتح Developer Tools في المتصفح لمراقبة طلبات AJAX\n";

echo "\n=== انتهى الاختبار ===\n";

// 6. خطوات الاختبار اليدوي
echo "\n6. خطوات الاختبار اليدوي:\n";
echo "1. اذهب إلى POS ADD\n";
echo "2. اختر مستودع\n";
echo "3. أضف منتجات للسلة\n";
echo "4. اختر عميل لديه صلاحية delivery\n";
echo "5. لاحظ ظهور التنبيه الأصفر\n";
echo "6. لاحظ تغيير نص الزر إلى 'حفظ طلب التوصيل'\n";
echo "7. انقر على الزر\n";
echo "8. راقب Network tab في Developer Tools\n";
echo "9. تحقق من الاستجابة والإعادة التوجيه\n";

echo "\n=== تم ===\n";
